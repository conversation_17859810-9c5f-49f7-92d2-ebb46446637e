# frozen_string_literal: true

require 'zip' # For zipping assets

module Admin
  class PatientsController < Admin::ApplicationController
    include Admin::ConversationsHelper
    include Admin::PatientAssetsHelper
    include Admin::ActionsHelper

    before_action :set_patient, only: %i[
      show update conversation linked_conversation actions assets
      update_asset_labels archive_assets forgot_password reset_password
      prescriptions archive toggle_archive update_teethgrid_view update_field
      update_assigned_staff update_assigned_practices remove_practice add_payment_plan
      remove_payment_plan remove_team_member login_as_patient available_devices
      download_assets notes
    ]
    before_action :set_users_and_practices, only: %i[new show create update]
    before_action :set_alerts, only: %i[new show create update]
    before_action :set_gps, only: %i[new show create update]
    before_action :set_payment_plans, only: %i[new show create update]
    before_action :set_notes, only: %i[show update]

    include Admin::ConversationsHelper
    include Admin::PatientAssetsHelper
    include Admin::ActionsHelper

    def index
      @patients = policy_scope(Patient)
                  .includes(practices_patients: :practice)
                  .where(archived: params[:archived] == 'true', temporary: false)
                  .order('patients.created_at DESC')
                  .paginate(page: params[:page], per_page: 20)
    end

    def new
      @patient = Patient.new
    end

    def show
      @linked_family_members = [
        { id: 1, name: 'Sarah P.', dob_info: '15/06/1985 • 39y 11m', initials: 'SP' },
        { id: 2, name: 'Michael P.', dob_info: '22/09/1982 • 42y 8m', initials: 'MP' },
        { id: 3, name: 'Emma P.', dob_info: '03/12/2010 • 14y 5m', initials: 'EP' },
        { id: 4, name: 'John D.', dob_info: '18/04/2015 • 10y 1m', initials: 'JD' },
        { id: 5, name: 'Lisa M.', dob_info: '30/07/1990 • 34y 10m', initials: 'LM' },
        { id: 6, name: 'Robert K.', dob_info: '05/02/1978 • 47y 3m', initials: 'RK' }
      ]

      respond_to do |format|
        format.html

        # NOTE: used in Secure Send
        format.json do
          render json: { patient: @patient }
        end
      end
    end

    def linked_conversation
      conversation = @patient.linked_conversation
      render json: { conversation_id: conversation.id }
    rescue StandardError => e
      render json: { error: e.message }, status: :unprocessable_entity
    end

    def conversation
      # Eager load conversation and author associations to prevent N+1 queries
      @conversation = @patient.linked_conversation

      # Only load messages if conversation exists
      if @conversation
        @messages = @conversation.conversation_messages.includes(:author)
        @messages = @messages.where(label: params[:message_filter]).order(created_at: :asc) if params[:message_filter].present?
        if params[:filter].present?
          @messages = @messages.where(message_type: 'outbound',
                                      from: params[:filter]).or(@messages.where.not(message_type: 'outbound'))
        end

        @messages = @messages.order(created_at: :asc)
      else
        @messages = []
      end

      @whatsapp_templates = load_whatsapp_templates
      @letter_templates = load_letter_templates
    end

    def actions
      # Eager load created_by and actionable associations to prevent N+1 queries
      @actions = @patient.actions.includes(:created_by, :actionable).with_deleted
      @comments = fetch_comments(@actions)
    end

    def assets
      # Filter by archived status
      show_archived = params[:archived] == 'true'
      @assets = @patient.patient_assets.where(archived: show_archived).order('created_at DESC')

      # Filter by label if selected
      @assets = @assets.where(label: params[:label]) if params[:label].present? && params[:label] != 'all'

      @labels = [*DEFAULT_LABELS, *PatientAssetLabel.pluck(:label)]
      @selected_label = params[:label].presence || 'all'
      @show_archived = show_archived

      respond_to do |format|
        format.html
        format.json do
          # Format assets for JSON response
          assets_json = @assets.map do |asset|
            {
              id: asset.id,
              label: asset.label,
              filename: asset.file.filename.to_s,
              created_at: asset.created_at.strftime('%-d/%-m/%Y %H:%M'),
              badge_class: asset_badge_class(asset.label),
              file_url: asset.file.attached? ? url_for(asset.file) : nil,
              archived: asset.archived
            }
          end

          render json: {
            success: true,
            assets: assets_json,
            count: assets_json.length,
            show_archived: show_archived,
            selected_label: @selected_label
          }
        end
      end
    end

    def update_asset_labels
      asset_ids = params[:asset_ids]
      label = params[:label]

      if asset_ids.blank? || label.blank?
        render json: { success: false, message: 'Missing asset IDs or label' }, status: :unprocessable_entity
        return
      end

      begin
        updated_assets = PatientAsset.where(id: asset_ids)
        updated_assets.update_all(label: label)

        # Return updated assets with their new label for AJAX updates
        assets_data = updated_assets.reload.map do |asset|
          {
            id: asset.id,
            label: asset.label,
            badge_class: asset_badge_class(asset.label)
          }
        end

        render json: {
          success: true,
          message: 'Asset labels updated successfully',
          assets: assets_data
        }
      rescue StandardError => e
        render json: { success: false, message: "Error updating asset labels: #{e.message}" }, status: :unprocessable_entity
      end
    end

    def archive_assets
      asset_ids = params[:asset_ids]

      if asset_ids.blank?
        render json: { success: false, message: 'No assets selected' }, status: :unprocessable_entity
        return
      end

      begin
        # Convert the archived parameter to a boolean
        archived = params[:archived].to_s.downcase == 'true' || params[:archived] == true

        # If we're viewing archived assets and the action is to restore (archived=false)
        # or we're viewing non-archived assets and the action is to archive (archived=true)
        PatientAsset.where(id: asset_ids).update_all(archived: archived)

        message = archived ? 'Assets archived successfully' : 'Assets restored successfully'

        render json: {
          success: true,
          message: message,
          asset_ids: asset_ids
        }
      rescue StandardError => e
        render json: { success: false, message: "Error archiving assets: #{e.message}" }, status: :unprocessable_entity
      end
    end

    def prescriptions
      @prescriptions = @patient.prescriptions
    end

    def create_temporary
      @patient = Patient.new(
        first_name: 'New',
        last_name: 'Patient',
        temporary: true,
        created_by_id: current_user.id
      )

      if @patient.save(validate: false)
        # Schedule cleanup job to run after 15 minutes
        CleanupTemporaryPatientsJob.set(wait: 15.minutes).perform_later(@patient.id)

        redirect_to admin_patient_path(@patient)
      else
        flash[:error] = ['Error creating temporary patient', @patient.errors.full_messages.join(', ')]
        redirect_to admin_patients_path
      end
    end

    def create
      @patient = Patient.new(patient_params)
      @patient.created_by_id = current_user.id

      # If this was a temporary patient being saved properly, remove temporary flag
      @patient.temporary = false if @patient.temporary?

      if params[:saved]
        if @patient.save
          flash[:success] = 'Patient created'
          redirect_to admin_patients_path
        else
          flash[:error] = ['Error creating patient', @patient.errors.first&.full_message]
          render :new
        end
      else
        render :new
      end
    end

    def update
      # Handle JSON requests properly
      if request.content_type =~ /json/
        # Parse JSON request body for JSON requests
        json_params = JSON.parse(request.body.read)
        params.merge!(json_params) if json_params.is_a?(Hash)
      end

      @patient.assign_attributes(patient_params)

      # Clear temporary flag when saving with valid data
      @patient.temporary = false if @patient.temporary?

      respond_to do |format|
        if @patient.save
          format.html do
            flash[:success] = 'Patient updated'
            redirect_to admin_patient_path(@patient)
          end
          format.json do
            render json: { success: true, patient: @patient }
          end
          format.any do
            render json: { success: true, patient: @patient }
          end
        else
          format.html do
            flash[:error] = ['Error updating patient', @patient.errors.first&.full_message]
            render :show
          end
          format.json do
            render json: { success: false, errors: @patient.errors.full_messages }, status: :unprocessable_entity
          end
          format.any do
            render json: { success: false, errors: @patient.errors.full_messages }, status: :unprocessable_entity
          end
        end
      end
    rescue JSON::ParserError => e
      # Handle JSON parsing errors
      respond_to do |format|
        format.json { render json: { success: false, error: "Invalid JSON: #{e.message}" }, status: :bad_request }
        format.any { render json: { success: false, error: "Invalid JSON: #{e.message}" }, status: :bad_request }
      end
    end

    def forgot_password
      @patient.send_reset_password_instructions
      flash[:success] = 'Password reset email sent'
      redirect_to admin_patient_path(@patient)
    end

    def reset_password
      random_password = SecureRandom.hex(4)
      @patient.update(password: random_password, force_password_reset: true)

      render json: { password: random_password }
    end

    def search
      patients = policy_scope(Patient).ransack(fuzzy_search: params[:q] || params[:query]).result(distinct: true).limit(10)

      last_visited_patients = policy_scope(Patient).where(id: session[:last_visited_patients]).limit(10)

      respond_to do |format|
        format.html do
          render partial: 'admin/patients/search', locals: { patients: patients, last_visited_patients: last_visited_patients }
        end
        format.json { render partial: 'admin/patients/search', locals: { patients: patients } }
      end
    end

    def select2_search
      # Start with base patient scope
      patients_scope = policy_scope(Patient)

      # Filter by practice ID from the board if provided
      if params[:board_id].present?
        Rails.logger.info("Finding practice for board ID: #{params[:board_id]}")
        board = CrmBoard.find_by(id: params[:board_id])

        if board && board.practice_id.present?
          # Filter patients by practice ID
          practice_id = board.practice_id
          Rails.logger.info("Filtering patients by practice ID: #{practice_id} from board #{board.id}")

          # Only show patients that belong to this practice
          patients_scope = patients_scope.joins(:practices_patients)
                                         .where(practices_patients: { practice_id: practice_id })

          Rails.logger.info("Found patients for practice ID: #{practice_id}")
        else
          Rails.logger.warn("Board with ID #{params[:board_id]} not found or has no practice ID")
        end
      end

      # Apply search and return results
      results = patients_scope
                .fuzzy_search(params[:q])
                .limit(20)
                .map { |patient| { id: patient.id, text: patient.full_name_with_title_and_email } }

      render json: { :results => results }, status: :ok
    end

    def bypass_2fa
      return if current_patient.blank?

      session[:two_factor_verified_at] = Time.current
      redirect_to after_sign_in_path_for(current_patient)
    end

    def toggle_archive
      # Get the archived parameter from the request
      # For JSON requests, we need to parse the request body
      archived_param = nil

      if request.content_type =~ /json/
        # Parse JSON request body
        begin
          body = request.body.read
          json_params = JSON.parse(body) unless body.empty?
          archived_param = json_params['archived'] unless json_params.nil?
        rescue JSON::ParserError => e
          # Log the error but continue processing
          Rails.logger.error("JSON parse error in toggle_archive: #{e.message}")
        end
      else
        # Regular form parameters
        archived_param = params[:archived]
      end

      # Convert the parameter to a boolean if it exists
      if !archived_param.nil?
        archived_status = ActiveModel::Type::Boolean.new.cast(archived_param)

        # Update only the archived attribute and skip validations/callbacks
        if @patient.update_column(:archived, archived_status)
          respond_to do |format|
            format.json { render json: { success: true, archived: @patient.archived } }
            format.html { redirect_to admin_patient_path(@patient), notice: 'Patient status updated successfully.' }
          end
        else
          respond_to do |format|
            format.json { render json: { success: false, error: 'Failed to update patient status' }, status: :unprocessable_entity }
            format.html { redirect_to admin_patient_path(@patient), alert: 'Failed to update patient status.' }
          end
        end
      else
        respond_to do |format|
          format.json { render json: { success: false, error: 'Missing archived parameter' }, status: :bad_request }
          format.html { redirect_to admin_patient_path(@patient), alert: 'Missing archived parameter.' }
        end
      end
    end

    def archive
      if @patient.archived
        @patient.update(archived: false)
        flash[:success] = 'Patient unarchived'
      else
        @patient.update(archived: true)
        flash[:success] = 'Patient archived'
      end
      redirect_to admin_patient_path(@patient)
    end

    def update_teethgrid_view
      if @patient.update(hide_base_and_history_treatments: params[:hide_base_and_history_treatments])
        render json: { success: true }
      else
        render json: { success: false, error: @patient.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def update_field
      params_hash = patient_params.to_h

      if params_hash.present?
        field_name = params_hash.keys.first
        field_value = params_hash[field_name]

        consent_fields = %w[email_consent sms_consent whatsapp_consent instagram_consent facebook_consent marketing_consent]

        result = if consent_fields.include?(field_name) && field_name != 'consent_updated_at'
                   @patient.update_columns(
                     field_name.to_sym => field_value,
                     consent_updated_at: Time.current
                   )
                 else
                   @patient.update_column(field_name.to_sym, field_value)
                 end

        if result
          render json: { success: true, patient: { id: @patient.id, field_name => field_value } }
        else
          render json: { success: false, errors: ['Failed to update field'] }, status: :unprocessable_entity
        end
      else
        render json: { success: false, errors: ['No field provided for update'] }, status: :unprocessable_entity
      end
    end

    def add_payment_plan
      plan_ids = params[:patient][:cot_payment_plan_ids]

      if plan_ids.present?
        plan_id = plan_ids.first
        plan = CotPaymentPlan.find_by(id: plan_id)

        if plan && !@patient.cot_payment_plan_ids.include?(plan.id)
          @patient.cot_payment_plans << plan
          render json: { success: true }
        else
          render json: { success: false, message: 'Payment plan already added or not found' }
        end
      else
        render json: { success: false, message: 'No payment plan provided' }
      end
    end

    def remove_payment_plan
      plan_id = params[:plan_id]

      if plan_id.present?
        plan = CotPaymentPlan.find_by(id: plan_id)

        if plan && @patient.cot_payment_plan_ids.include?(plan.id.to_i)
          @patient.cot_payment_plans.delete(plan)
          render json: { success: true }
        else
          render json: { success: false, message: 'Payment plan not found or not assigned to patient' }
        end
      else
        render json: { success: false, message: 'No payment plan provided' }
      end
    end

    def update_assigned_staff
      # Get the assigned staff IDs from params
      staff_ids = params[:patient][:assigned_staff_ids] || []

      # Clear existing associations and create new ones directly
      ActiveRecord::Base.transaction do
        # Remove all existing associations
        @patient.patients_users.destroy_all

        # Fetch all users at once to avoid N+1 queries
        if staff_ids.any?
          users = User.where(id: staff_ids)

          # Create new associations for each selected staff member
          @patient.assigned_staff = users
        end
      end

      # Reload patient with eager loading to ensure we have the latest associations
      @patient.reload

      # Render the updated team members HTML
      team_html = render_to_string(partial: 'admin/patients/team_members', locals: { patient: @patient })
      render json: { success: true, message: 'Team members updated successfully', html: team_html }
    rescue StandardError => e
      render json: { success: false, error: "Failed to update team members: #{e.message}" }, status: :unprocessable_entity
    end

    def remove_team_member
      user_id = params[:user_id]
      return render json: { success: false, message: 'User ID is required' }, status: :bad_request if user_id.blank?

      begin
        # Find the specific team member association and destroy it
        association = @patient.patients_users.find_by(user_id: user_id)

        if association
          association.destroy
          @patient.reload

          team_html = render_to_string(partial: 'admin/patients/team_members', locals: { patient: @patient })
          if @patient.assigned_staff.any?
            # Render the updated team members HTML
            render json: { success: true, message: 'Team member removed successfully', html: team_html, has_team_members: true }
          else
            # If no team members left, render the empty state
            render json: { success: true, message: 'Team member removed successfully', html: team_html, has_team_members: false }
          end
        else
          render json: { success: false, message: 'Team member not found for this patient' }, status: :not_found
        end
      rescue StandardError => e
        render json: { success: false, error: "Failed to remove team member: #{e.message}" }, status: :unprocessable_entity
      end
    end

    def update_assigned_practices
      # Get the assigned practice IDs from params
      practice_ids = params[:patient][:practice_ids] || []

      # Clear existing associations and create new ones directly
      ActiveRecord::Base.transaction do
        # Remove all existing associations
        @patient.practices_patients.destroy_all

        # Fetch all practices at once to avoid N+1 queries
        if practice_ids.any?
          practices = Practice.where(id: practice_ids)

          # Create new associations for all selected practices at once
          @patient.practices = practices
        end
      end

      # Reload patient with eager loading to ensure we have the latest associations
      @patient.reload

      # Render the updated practice members HTML
      practices_html = render_to_string(partial: 'admin/patients/practice_members', locals: { patient: @patient })
      render json: { success: true, message: 'Practices updated successfully', html: practices_html }
    rescue StandardError => e
      render json: { success: false, error: "Failed to update practices: #{e.message}" }, status: :unprocessable_entity
    end

    def remove_practice
      practice_id = params[:practice_id]

      if practice_id.blank?
        render json: { success: false, message: 'Practice ID is required' }, status: :bad_request
        return
      end

      practice = Practice.find_by(id: practice_id)
      if practice.nil?
        render json: { success: false, message: 'Practice not found' }, status: :not_found
        return
      end

      # Check if the practice is actually assigned to this patient
      unless @patient.practices.include?(practice)
        render json: { success: false, message: 'Practice is not assigned to this patient' }, status: :bad_request
        return
      end

      # Remove the practice from the patient
      ActiveRecord::Base.transaction do
        @patient.practices.delete(practice)
      end

      # Reload patient to ensure we have the latest associations
      @patient.reload

      # Render the updated practice members HTML
      practices_html = render_to_string(partial: 'admin/patients/practice_members', locals: { patient: @patient })
      render json: { success: true, message: 'Practice removed successfully', html: practices_html }
    rescue StandardError => e
      render json: { success: false, error: "Failed to remove practice: #{e.message}" }, status: :unprocessable_entity
    end

    # Search method moved/combined with the one at line ~257 that renders a partial

    def linked_family
      # For now, we'll return a mock response with sample data
      # In a real implementation, this would fetch actual linked family members from the database
      family_members = []

      # Render JSON response with family members
      render json: { success: true, family_members: family_members }
    rescue StandardError => e
      render json: { success: false, error: "Failed to fetch linked family: #{e.message}" }, status: :unprocessable_entity
    end

    def update_linked_family
      # Get the linked family IDs from params
      family_ids = params[:patient][:linked_family_ids] || []

      # In a real implementation, this would update the linked family relationships in the database
      # For now, we'll just return a success response with mock data

      # Create sample family members data for the response
      @linked_family_members = []
      if family_ids.any?
        # In a real implementation, this would fetch the actual patient records
        # For now, we'll create sample data based on the IDs
        family_ids.each_with_index do |id, index|
          @linked_family_members << {
            id: id,
            name: "Family Member #{index + 1}",
            dob_info: "01/01/2000 • #{25 - index}y 0m",
            initials: 'FM'
          }
        end
      end

      # Render the updated family members HTML
      family_html = render_to_string(partial: 'admin/patients/family_members', locals: { linked_family_members: @linked_family_members })
      render json: { success: true, message: 'Family members updated successfully', html: family_html }
    rescue StandardError => e
      render json: { success: false, error: "Failed to update family members: #{e.message}" }, status: :unprocessable_entity
    end

    # Handle login as patient request and trigger Pusher notification
    def available_devices
      # Print detailed debug info
      Rails.logger.debug "\n==== DEVICE SELECTION DEBUG ===="
      Rails.logger.debug "Patient ID: #{@patient.id}, Name: #{@patient.first_name} #{@patient.last_name}"
      # Find the patient's practices (many-to-many relationship)
      practices = @patient.practices
      Rails.logger.debug "Patient belongs to #{practices.count} practices: #{practices.map { |p| "#{p.id} (#{p.name})" }.join(', ')}"
      # If patient has no practices, try to find all practices
      if practices.empty?
        Rails.logger.debug 'Patient has no practices, checking all practices'
        practices = Practice.all
        Rails.logger.debug "Found #{practices.count} total practices: #{practices.map { |p| "#{p.id} (#{p.name})" }.join(', ')}"
      end

      # Get the current practice if set
      current_practice = nil
      if @patient.current_practice_id.present?
        current_practice = @patient.current_practice
        Rails.logger.debug "Patient has current_practice_id: #{@patient.current_practice_id} (#{current_practice&.name || 'not found'})"
      else
        Rails.logger.debug 'Patient has no current_practice_id set'
      end

      # Add current practice to the list if it exists and isn't already included
      if current_practice && !practices.include?(current_practice)
        practices << current_practice
        Rails.logger.debug 'Added current practice to the list'
      end

      # Initialize an empty array for devices
      all_devices = []

      # Get all active and available registered devices for each practice
      practices.each do |practice|
        practice_devices = practice.registered_devices.active.where(status: 'available')
        Rails.logger.debug "Practice #{practice.id} (#{practice.name}) has #{practice_devices.count} active and available devices"

        next unless practice_devices.any?

        practice_devices.each do |device|
          Rails.logger.debug "  - Device ID: #{device.id}, Name: #{device.name}, Active: #{device.active}, Status: #{device.status}"
        end
        all_devices.concat(practice_devices)
      end

      # Get any patient-specific devices that are available
      patient_devices = @patient.registered_devices.active.where(status: 'available')
      Rails.logger.debug "Patient #{@patient.id} has #{patient_devices.count} active and available devices"
      if patient_devices.any?
        patient_devices.each do |device|
          Rails.logger.debug "  - Device ID: #{device.id}, Name: #{device.name}, Active: #{device.active}, Status: #{device.status}"
        end
        all_devices.concat(patient_devices)
      end

      # Remove duplicates and sort by name
      devices = all_devices.uniq.sort_by(&:name)
      Rails.logger.debug "Total unique devices found: #{devices.count}"
      Rails.logger.debug "==== END DEBUG ====\n"
      # Return the devices as JSON
      render json: {
        success: true,
        devices: devices.as_json(only: %i[id name device_token last_used_at status])
      }
    rescue StandardError => e
      # Log the error
      Rails.logger.error("Error fetching available devices: #{e.message}")

      # Return error response
      render json: { success: false, message: e.message }, status: :internal_server_error
    end

    def login_as_patient
      # Get the selected device if provided
      device_id = params[:device_id]
      device = RegisteredDevice.find_by(id: device_id) if device_id.present?
      pusher = PusherService.new

      notification_data = {
        patient_id: @patient.id,
        patientId: @patient.id, # Add both formats for compatibility
        requested_by: current_user.id,
        requested_by_name: current_user.full_name,
        timestamp: Time.current.to_i
      }

      if device.present? && device.status == 'available'
        # Send to specific device channel
        device_channel = "private-device-#{device.device_token}"
        notification_data[:device_id] = device.id
        notification_data[:device_token] = device.device_token

        # Check if device already has a patient association
        if device.patient_id.blank?
          # Associate the device with this patient
          device.update(patient_id: @patient.id, last_used_at: Time.current)
          Rails.logger.info("[DEVICE AUTH] Associated device #{device.id} with patient #{@patient.id} during login request")
        else
          # Just update the last used timestamp
          device.update(last_used_at: Time.current)
        end

        # Trigger on device-specific channel
        Rails.logger.info("[DEVICE AUTH] Sending login request to device channel: #{device_channel} for patient #{@patient.id}")
        pusher.trigger(device_channel, 'login_request', notification_data)

        flash[:notice] = "Login request sent to device: #{device.name}"
      else
        # Send to current device channel (any device on the sign-in page)
        notification_data[:send_to_current_device] = true

        # Trigger on current-device channel
        Rails.logger.info("[DEVICE AUTH] Sending login request to current device channel for patient #{@patient.id}")
        pusher.trigger('patient-signin-current-device', 'login_request', notification_data)

        flash[:notice] = 'Login request sent to any device currently on the sign-in page'
      end

      # Return success response
      render json: { success: true, message: 'Login request sent successfully' }
    rescue StandardError => e
      # Log the error
      Rails.logger.error("Error sending login as patient notification: #{e.message}")

      # Return error response
      render json: { success: false, message: e.message }, status: :unprocessable_entity
    end

    def download_assets
      # Get selected labels from params
      selected_labels = params[:labels] || []

      if selected_labels.empty?
        flash[:error] = 'No labels selected for download.'
        redirect_to admin_patient_assets_path(@patient)
        return
      end

      assets_to_download = @patient.patient_assets.where(label: selected_labels).with_attached_file

      if assets_to_download.empty?
        flash[:notice] = 'No assets found for the selected labels for this patient.'
        redirect_to admin_patient_assets_path(@patient)
        return
      end

      patient_name_sanitized = @patient.full_name.gsub(/[^0-9A-Za-z.-]/, '_')
      timestamp = Time.current.strftime('%Y%m%d%H%M%S')
      zip_filename = "#{patient_name_sanitized}_assets_#{timestamp}.zip"

      # Create zip file in memory using StringIO
      zip_data = create_zip_from_assets(assets_to_download)

      # Send the data directly to the browser
      send_data zip_data,
                type: 'application/zip',
                disposition: 'attachment',
                filename: zip_filename

      # Explicitly return to prevent any further processing
      nil
    end

    # Helper method to create zip file in memory
    def create_zip_from_assets(assets)
      # Create a new StringIO object to store the zip file
      zip_stream = StringIO.new

      # Create a new zip file in the StringIO
      Zip::OutputStream.write_buffer(zip_stream) do |zos|
        assets.each do |asset|
          next unless asset.file.attached?

          original_filename = asset.file.filename.to_s
          sanitized_label = asset.label.gsub(/[^0-9A-Za-z.-]/, '_')
          entry_name = File.join(sanitized_label, original_filename)

          zos.put_next_entry(entry_name)

          # Download the file content and write it to the zip
          asset.file.download do |chunk|
            zos.write(chunk)
          end
        end
      end

      # Rewind the StringIO and return its content
      zip_stream.rewind
      zip_stream.read
    end

    # AJAX endpoint to fetch patient notes (archived or active)
    def notes
      archived = params[:archived] == 'true'
      notes = @patient.patient_notes.includes(:user).where(archived: archived, updated: false).order(created_at: :desc)

      respond_to do |format|
        format.json do
          render json: {
            success: true,
            notes: notes.map do |note|
              {
                id: note.id,
                title: note.title,
                text: note.text,
                color: note.color,
                pinned: note.pinned,
                archived: note.archived,
                user_name: note.user&.full_name || 'Admin User',
                created_at: note.created_at
              }
            end
          }
        end
      end
    end

    private

    def set_patient
      # Eager load necessary associations to prevent N+1 queries
      @patient = Patient.includes(:alerts, :cot_payment_plans, :patient_gp, practices_patients: :practice)
                        .find(params[:id])

      self.page_title = @patient.full_name_with_title
    end

    def set_users_and_practices
      @users = User.all
      @practices = current_user.practices
    end

    def set_alerts
      @alerts = policy_scope(Alert)
    end

    def set_gps
      @patient_gps = policy_scope(PatientGp)
    end

    def set_payment_plans
      @payment_plans = policy_scope(CotPaymentPlan)
    end

    def set_notes
      # Eager load user associations to prevent N+1 queries
      @archived_notes = @patient.patient_notes.includes(:user).where(archived: true).order(created_at: :desc)
      @archived_primary_notes = @archived_notes.where(updated: false)
      @notes = @patient.patient_notes.includes(:user).where(archived: false, updated: false).order(created_at: :desc)
    end

    def patient_params
      params.require(:patient).permit(
        :title,
        :first_name,
        :middle_name,
        :last_name,
        :previous_last_name,
        :preferred_name,
        :date_of_birth,
        :email,
        :mobile_phone,
        :alternative_phone,
        :work_phone,
        :ethnicity,
        :biological_sex,
        :gender,
        :pronouns,
        :ni_number,
        :insurance_number,
        :address_line_1,
        :address_line_2,
        :county,
        :city,
        :postcode,
        :country,
        :school_name,
        :school_phone_number,
        :school_address_line_1,
        :school_address_line_2,
        :school_county,
        :school_country,
        :school_city,
        :school_postcode,
        :image,
        :sms_consent,
        :email_consent,
        :whatsapp_consent,
        :instagram_consent,
        :emergency_contact_name,
        :emergency_contact_number,
        :facebook_consent,
        :marketing_consent,
        :patient_gp_id,
        assigned_staff_ids: [],
        practice_ids: [],
        alert_ids: [],
        cot_payment_plan_ids: []
      )
    end
  end
end
