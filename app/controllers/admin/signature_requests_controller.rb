# frozen_string_literal: true

# app/controllers/admin/signature_requests_controller.rb
module Admin
  class SignatureRequestsController < Admin::ApplicationController
    include Rails.application.routes.url_helpers

    before_action :set_signature_request, only: %i[show edit update_status update resend_via_email resend_via_sms]
    before_action { self.page_title = 'Secure Send' }

    def new
      @templates = DocumentTemplate.all
      @treatment_plans = TreatmentPlanOption.where(status: 'New')
      @signature_request = SignatureRequest.new
    end

    def create
      result = {}

      @patient = Patient.find(params[:signature_request][:user_id])

      if params.dig(:signature_request, :templates).present?
        templates = params.dig(:signature_request, :templates).split(',')
        templates.each do |key|
          @service = create_service(
            title: 'document',
            document_template_id: key,
            template_text: params.dig(:signature_request, :template_text, key),
            due_by: params[:signature_request][:due_by],
            letter_fields: params.dig(:signature_request, :letter_fields),
            charting_appointment_id: params.dig(:signature_request, :charting_appointment_id),
            sent_at: params.dig(:signature_request, :sent_at)
          )
          result = @service.create_signable_document_and_request
        end
      else
        @service = create_service(
          title: 'document',
          treatment_plan_option_id: params.dig(:signature_request, :treatment_plan_templates),
          due_by: params[:signature_request][:due_by],
          charting_appointment_id: params.dig(:signature_request, :charting_appointment_id),
          sent_at: params.dig(:signature_request, :sent_at)
        )
        result = @service.create_signable_document_and_request
      end

      if result[:success]
        redirect_to admin_signature_requests_path, notice: 'Signature request created successfully.'
      else
        redirect_to admin_signature_requests_path, alert: result[:error]
      end
    end

    def index
      @all_requests = SignatureRequest.includes(:patient, :signable_document)
      @ready_to_send = @all_requests.where(status: 'ready_to_send')
      @awaiting_signature = @all_requests.pending
      @late = @all_requests.late
      @completed = @all_requests.where(status: 'completed')
      @closed = @all_requests.where(status: 'closed')
      @archived = @all_requests.where(status: 'archived')
    end

    def edit
      @templates = DocumentTemplate.all
      @treatment_plans = TreatmentPlanOption.where(status: 'New')
    end

    def update
      key = @signature_request.signable_document.document_template_id
      attrs = {
        patient_id: params.dig(:signature_request, :user_id),
        template_text: params.dig(:signature_request, :template_text, key&.to_s),
        due_by: params.dig(:signature_request, :due_by),
        charting_appointment_id: params.dig(:signature_request, :charting_appointment_id),
        sent_at: params.dig(:signature_request, :sent_at),
        status: 'pending'
      }

      attrs[:sent_by] = current_user if params.dig(:signature_request, :sent_at).blank?
      attrs[:status] = 'ready_to_send' if params.dig(:signature_request, :sent_at).present?

      if @signature_request.update!(attrs)
        if params.dig(:signature_request, :sent_at).blank?
          SecureSend::SigningNotificationService.new(@signature_request).send_signing_request
        end

        redirect_to admin_signature_requests_path, notice: 'Signature request updated successfully.'
      else
        redirect_to admin_signature_requests_path, alert: "Signature request wasn't updated successfully."
      end
    end

    def update_status
      if @signature_request.update(signature_request_status_params)
        respond_to do |format|
          flash[:notice] = 'Status successfully updated'
          format.html do
            redirect_to admin_signature_requests_url
          end
          format.json do
            render json: { success: true, redirect_url: admin_signature_requests_url }, status: :ok
          end
        end
      else
        respond_to do |format|
          format.html do
            flash[:alert] = @signature_request.errors.full_messages.to_sentence
            redirect_to admin_signature_requests_url
          end
          format.json do
            render json: { success: false, message: @signature_request.errors.full_messages.to_sentence }, status: :unprocessable_entity
          end
        end
      end
    end

    def show
      # @signature_request is set by before_action
    end

    def resend_via_email
      @patient = @signature_request.patient
      @service = create_service
      result = @service.resend_notification_via_email(@signature_request)

      notice_message = result[:previously_sent] ? 'Notification has been resent.' : 'Notification has been sent.'
      redirect_to admin_signature_requests_path, notice: notice_message
    end

    def resend_via_sms
      @patient = @signature_request.patient
      @service = create_service
      result = @service.resend_notification_via_sms(@signature_request)

      notice_message = result[:previously_sent] ? 'Notification has been resent.' : 'Notification has been sent.'
      redirect_to admin_signature_requests_path, notice: notice_message
    end

    def fetch_patient_appointments
      charting_appointments = ChartingAppointment
                              .includes(:dentist, :calendar_booking, charted_treatments: :treatment)
                              .joins(:course_of_treatment)
                              .where(course_of_treatments: { patient_id: params[:patient_id] })
                              .where.not(calendar_booking_id: nil)
                              .where(completed: true)

      appointments = charting_appointments.map do |appointment|
        dentist = appointment.dentist
        treatments = appointment.charted_treatments.map(&:treatment).pluck(:patient_friendly_name).uniq
        booking = appointment.calendar_booking

        {
          appt_id: appointment.id,
          dentist_first_name: dentist.first_name,
          dentist_last_name: dentist.last_name,
          role: dentist.roles.first&.name,
          treatments: treatments,
          calendar_booking_duration: booking.duration_in_minutes,
          calendar_booking_start: booking.start_time.strftime('%b %d, %Y')
        }
      end

      render json: { status: 'success', appointments: appointments }
    end

    def get_document_preview
      patient = Patient.find(params[:patient_id])
      original_text = params[:text]
      parsed_text = ::Notifications::PlaceholderConverter.convert(patient, original_text)

      render json: { status: 'success', text: parsed_text }
    end

    def pdf_preview
      option = TreatmentPlanOption.find_by(id: params[:id])

      if option&.pdf_file&.attached?
        pdf_url = url_for(option.pdf_file)
        render json: { pdf_url: pdf_url }
      else
        render json: { error: 'PDF not found' }, status: :not_found
      end
    end

    private

    def fetch_patients(practices)
      Patient.joins(:practices_patients)
             .where(practices_patients: { practice_id: practices.pluck(:id) })
             .uniq
             .map { |user| { name: user.full_name, id: user.id } }
    end

    def set_signature_request
      @signature_request = SignatureRequest.find(params[:id])
    end

    def signature_request_status_params
      params.require(:signature_request).permit(:status)
    end

    def create_service(options = {})
      SecureSend::AdminSignatureRequestService.new(
        @patient,
        current_user,
        options
      )
    end
  end
end
