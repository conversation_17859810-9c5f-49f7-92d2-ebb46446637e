# frozen_string_literal: true

module Notifications
  class PlaceholderConverter
    include ActionView::Helpers::OutputSafetyHelper
    include Rails.application.routes.url_helpers

    attr_accessor :account, :model, :recipient, :template, :html

    def self.convert(model, template)
      new(model, template).convert
    end

    def initialize(model, template)
      @model = model
      @template = template
    end

    def convert
      str = template

      case model
      when Payment
        str.gsub!('{{payment.amount}}', model.amount.to_s)
        str.gsub!('{{payment.payment_date}}', model.payment_date.to_s)
        str.gsub!('{{payment.payment_method}}', model.payment_method.to_s)
        str.gsub!('{{payment.payment_type}}', model.payment_type.to_s)
        str.gsub!('{{payment.status}}', model.status.to_s)
        str.gsub!('{{payment.summary}}', model.summary.to_s)
        str.gsub!('{{payment.currency}}', model.currency.to_s)
        str.gsub!('{{payment.invoice.total}}', model.invoice.total.to_s)
        str.gsub!('{{payment.invoice.amount_due}}', model.invoice.amount_due.to_s)
        str.gsub!('{{payment.invoice.notes}}', model.invoice.notes.to_s)
        str.gsub!('{{payment.patient.full_name}}', model.patient.full_name.to_s)
        str.gsub!('{{payment.patient.pronouns}}', model.patient.pronouns.to_s)
        str.gsub!('{{payment.patient.email}}', model.patient.email.to_s)
        str.gsub!('{{payment.patient.phone}}', model.patient.mobile_phone.to_s)
      when Invoice
        str.gsub!('{{invoice.total}}', model.total.to_s)
        str.gsub!('{{invoice.amount_due}}', model.amount_due.to_s)
        str.gsub!('{{invoice.notes}}', model.notes.to_s)
        str.gsub!('{{invoice.patient.full_name}}', model.patient.full_name.to_s)
        str.gsub!('{{invoice.patient.pronouns}}', model.patient.pronouns.to_s)
        str.gsub!('{{invoice.patient.email}}', model.patient.email.to_s)
        str.gsub!('{{invoice.patient.phone}}', model.patient.mobile_phone.to_s)
      when Patient
        str.gsub!('{{patient.first_name}}', model.first_name.to_s)
        str.gsub!('{{patient.last_name}}', model.last_name.to_s)
        str.gsub!('{{patient.full_name}}', model.full_name.to_s)
        str.gsub!('{{patient.pronouns}}', model.pronouns.to_s)
        str.gsub!('{{patient.email}}', model.email.to_s)
        str.gsub!('{{patient.phone}}', model.mobile_phone.to_s)
        str.gsub!('{{medical_history_url}}', new_patients_medical_history_url(patient_id: model, host: ENV['HOST']))
      when TreatmentPlan
        str.gsub!('{{patient.full_name}}', model.patient.full_name.to_s)
        str.gsub!('{{patient.pronouns}}', model.patient.pronouns.to_s)
        str.gsub!('{{patient.email}}', model.patient.email.to_s)
        str.gsub!('{{patient.phone}}', model.patient.mobile_phone.to_s)
        str.gsub!('{{treatment_plan.url}}',
                  treatment_plan_options_patients_treatment_plans_url(id: model.patient.id, plan_id: model.id,
                                                                      practice_id: model.practice_id,
                                                                      host: ENV['HOST']))
      when Recall
        str.gsub!('{{recall.patient.full_name}}', model.patient.full_name.to_s)
        str.gsub!('{{recall.treatment.patient_friendly_name}}', model.treatment&.patient_friendly_name.to_s)
        str.gsub!('{{recall.practice.name}}', model.practice.name.to_s)
        str.gsub!('{{recall.practice.phone}}', model.practice.phone.to_s)
        str.gsub!('{{new_patient_session_url}}', Rails.application.routes.url_helpers.new_patient_session_url(host: ENV['HOST']))
      end

      str
    end
  end
end
