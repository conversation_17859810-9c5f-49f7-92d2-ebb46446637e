<!DOCTYPE html>
<html lang="en">
<head>
  <title><%= page_title %></title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet"/>
  <%= stylesheet_link_tag "https://cdn.jsdelivr.net/npm/gridstack@12.0.0/dist/gridstack.min.css", media: "all" %>
  <script src="https://connect-js.stripe.com/v1.0/connect.js" async></script>
  <script src="https://js.stripe.com/v3/" async></script>
  <script src="https://cdn.tiny.cloud/1/h8ksxv4rsqlbi0orn42moa9x8kh5ds8kbykaucvfdjnypo4u/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
  <script src="https://kit.fontawesome.com/ac6ae569bf.js" crossorigin="anonymous"></script>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
  <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
  <link href="https://unpkg.com/cropperjs/dist/cropper.min.css" rel="stylesheet">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

 <%= javascript_include_tag "application", "data-turbo-track": "reload" %>

 <% if controller_path.start_with?("admin/crm") ||
       controller_path.start_with?("admin/conversations") ||
       controller_path.start_with?("admin/patients") ||
       controller_path.start_with?("admin/prescriptions") ||
       controller_path.start_with?("admin/calendar") ||
       controller_path.start_with?("admin/treatment_plans") ||
       controller_path.start_with?("admin/treatment_plan_options") ||
       controller_path.start_with?("admin/lab_works") ||
       controller_path.start_with?("admin/lab_dockets") ||
       controller_path.start_with?("admin/signature_requests") ||
       controller_path.start_with?("admin/general_settings") ||
       controller_path.include?("/charting")  %>
  <%= stylesheet_link_tag "stylesheets/admin/tailwind", "data-turbo-track": "reload" %>

  <!-- Tailwind theme for Select2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/erimicel/select2-tailwindcss-theme/dist/select2-tailwindcss-theme-plain.min.css">
 <% else %>
  <%= stylesheet_link_tag "stylesheets/admin/application", "data-turbo-track": "reload" %>
 <% end %>

 <script src="https://cdn.tiny.cloud/1/h8ksxv4rsqlbi0orn42moa9x8kh5ds8kbykaucvfdjnypo4u/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
 <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.6/Sortable.js"></script>
 <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

</head>

<%= javascript_tag nonce: true do %>
  window._availablePractices = <%= raw(current_user.practices.map { |pr| { name: pr.name, id: pr.id } }.to_json) %>;
  <% if Current.practice_id %>
    window._currentPracticeId = <%= Current.practice_id %>;
  <% end %>
  window._currentUserId = <%= current_user.id %>;
  window._currentUserName = "<%= current_user.full_name %>";
  window._pusherApp = "<%= ENV["PUSHER_KEY"] %>";
  window._patientId = "<%= params[:patient_id] %>";
  window._deepgramApiKey = "<%= ENV["DEEPGRAM_API_KEY"] %>";
  window._currentPracticeId = <%= Current.practice_id.present? ? Current.practice_id : 'null' %>;
<% end %>

<% if controller_path.start_with?("admin/crm") ||
     controller_path.start_with?("admin/conversations") ||
     controller_path.start_with?("admin/patients") ||
     controller_path.start_with?("admin/prescriptions") ||
     controller_path.start_with?("admin/calendar") ||
     controller_path.start_with?("admin/treatment_plans") ||
     controller_path.start_with?("admin/treatment_plan_options") ||
     controller_path.start_with?("admin/lab_works") ||
     controller_path.start_with?("admin/lab_dockets") ||
     controller_path.start_with?("admin/signature_requests") ||
     controller_path.start_with?("admin/general_settings") ||
     controller_path.include?("/charting")  %>
  <%= render "layouts/admin/tw_navbar" %>
  <%= render "layouts/admin/notifications" %>
  <%= render "layouts/admin/actions_sidebar" %>
<% else %>
  <%= render "layouts/admin/navbar" %>
  <%= render "layouts/admin/notifications" %>
  <%= render "layouts/admin/actions_sidebar" %>
  <%= render "layouts/admin/today_offcanvas" %>
<% end %>


<body class="container-fluid adminbackground">
<main class="main-content bg-[#f5f5f7] flex flex-col min-h-[calc(100vh-56px)]">

  <% if flash.any? %>
    <script type="text/javascript">
      document.addEventListener("DOMContentLoaded", function() {
        <% flash.each do |key, value| %>
          <% type = key.to_s.gsub('alert', 'error').gsub('notice', 'success') %>
          <% if value.is_a?(Array) %>
            toastr['<%= type %>']('<%= value[1] %>', '<%= value[0] %>');
          <% else %>
            toastr['<%= type %>']('<%= value %>');
          <% end %>
        <% end %>
      });
    </script>
  <% end %>

  <%= yield %>
  <%= render "admin/shared/truncate_modal" %>
</main>
  <!-- Calendar Sidebar Container -->
  <div id="calendar-sidebar-container"></div>

  <!-- Shared overlay for all sidebars -->
  <div id="shared-sidebar-overlay" class="fixed inset-0 bg-black/40 backdrop-blur-sm z-10 hidden" style="opacity: 0; transition: opacity 0.3s ease-in-out;"></div>
</body>
</html>
