<!-- Toast Container -->
<div class="fixed top-4 right-4 z-50 flex flex-col gap-2" id="notification-toast-container"></div>

<script>
  // Direct notification toggle function
  function toggleNotificationGroup(button, type, groupSize) {
    console.log('Direct toggle clicked for: ' + type);
    
    // Find the expanded section for this notification type
    var expanded = document.querySelector('.notification-group-expanded[data-notification-type="' + type + '"]');
    var notificationGroup = button.closest('.notification-group');
    
    if (!expanded || !notificationGroup) {
      console.error('Could not find expanded section or notification group for: ' + type);
      return;
    }
    
    // Toggle visibility
    var isHidden = expanded.classList.contains('hidden');
    
    if (isHidden) {
      // Show the expanded section
      expanded.classList.remove('hidden');
      
      // Remove stacked effect from the first notification
      var firstNotification = notificationGroup.querySelector('.relative[data-notification-id]');
      if (firstNotification) {
        // Find and remove the stacked elements
        var stackedElements = firstNotification.querySelectorAll('.absolute.inset-0.z-0');
        stackedElements.forEach(function(el) {
          el.style.display = 'none';
        });
      }
      
      // Rotate the chevron
      var chevron = button.querySelector('svg');
      if (chevron) chevron.classList.add('rotate-180');
      
      // Update the text
      var span = button.querySelector('span');
      if (span) span.textContent = 'Hide ' + (groupSize - 1) + ' ' + humanizeString(type) + ' notifications';
      
      console.log('Expanded group: ' + type);
    } else {
      // Hide the expanded section
      expanded.classList.add('hidden');
      
      // Restore stacked effect to the first notification
      var firstNotification = notificationGroup.querySelector('.relative[data-notification-id]');
      if (firstNotification) {
        // Find and show the stacked elements
        var stackedElements = firstNotification.querySelectorAll('.absolute.inset-0.z-0');
        stackedElements.forEach(function(el) {
          el.style.display = '';
        });
      }
      
      // Rotate the chevron back
      var chevron = button.querySelector('svg');
      if (chevron) chevron.classList.remove('rotate-180');
      
      // Update the text
      var span = button.querySelector('span');
      if (span) span.textContent = (groupSize - 1) + ' more ' + humanizeString(type) + ' notifications';
      
      console.log('Collapsed group: ' + type);
    }
  }

  // Helper function to humanize strings
  function humanizeString(str) {
    if (!str) return '';
    return str.replace(/_/g, ' ').replace(/\b\w/g, function(l) { return l.toUpperCase(); });
  }
</script>

<!-- Notification Sidebar -->
<div id="notification-sidebar" class="fixed inset-y-0 right-0 w-full sm:w-[480px] bg-white shadow-xl transform transition-transform duration-300 ease-in-out z-50 h-full translate-x-full">
  <div class="flex flex-col h-full">
    <div class="flex items-center justify-between p-4 border-b">
      <div class="flex items-center gap-3 bg-[#F2F2F7] p-1.5 rounded-xl">
        <button class="h-8 flex items-center justify-center transition-all duration-300 font-medium text-sm text-[#3A3A3C] hover:bg-[#E5E5EA] px-4 rounded-lg">Today</button>
        <button class="h-8 flex items-center justify-center transition-all duration-300 font-medium text-sm bg-white text-[#007AFF] px-5 rounded-lg shadow-[0_2px_8px_rgba(0,0,0,0.08)] flex-1">All</button>
      </div>
      <button id="close-notifications" class="text-gray-500 hover:text-gray-700" aria-label="Close notifications">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x h-5 w-5">
          <path d="M18 6 6 18"></path>
          <path d="m6 6 12 12"></path>
        </svg>
      </button>
    </div>
    <div class="px-4 py-2 border-b">
      <button class="inline-flex items-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-9 rounded-md px-3 text-[#007AFF] hover:text-[#0056b3] hover:bg-[#F0F7FF] w-full justify-start">Mark
        all as read
      </button>
    </div>
    <div class="flex-1 overflow-y-auto p-4">
      <div class="space-y-6" id="notification-group-list">
        <% Notification.grouped_for_display(@notifications || []).each do |type, notifications| %>
          <div class="relative notification-group" data-notification-type="<%= type %>">
            <% notification = notifications.first %>
            <% partial_name = "layouts/admin/notification_#{type}" %>
            <% stacked = notifications.size > 1 %>
            <% if lookup_context.exists?(partial_name, [], true) %>
              <%= render partial: partial_name, locals: { notification: notification, stacked: stacked, notifications: notifications } %>
            <% else %>
              <%= render partial: 'layouts/admin/notification', locals: { notification: notification, stacked: stacked, notifications: notifications } %>
            <% end %>
            <% if notifications.size > 1 %>
              <div class="flex justify-center items-center w-full py-2 mt-4 mb-6 border rounded-xl cursor-pointer transition-colors shadow-sm bg-blue-50/70 text-blue-700 border-blue-200 hover:bg-blue-100/80 notification-more-btn" data-notification-type="<%= type %>" data-group-size="<%= notifications.size %>" onclick="toggleNotificationGroup(this, '<%= type %>', <%= notifications.size %>)">
                <span class="font-medium text-xs"><%= notifications.size - 1 %> more <%= type.humanize %> notifications</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3.5 w-3.5 ml-1.5">
                  <path d="m6 9 6 6 6-6"></path>
                </svg>
              </div>
              <div class="hidden space-y-4 notification-group-expanded" data-notification-type="<%= type %>">
                <% notifications.drop(1).each do |n| %>
                  <% if lookup_context.exists?(partial_name, [], true) %>
                    <%= render partial: partial_name, locals: { notification: n, stacked: false } %>
                  <% else %>
                    <%= render partial: 'layouts/admin/notification', locals: { notification: n, stacked: false } %>
                  <% end %>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>


