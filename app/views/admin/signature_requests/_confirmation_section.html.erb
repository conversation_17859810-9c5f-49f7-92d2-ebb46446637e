<div class="flex flex-1 p-6" id='confirmation-section'>
  <div class="w-full">
    <div class="mb-6">
      <h1 class="text-2xl font-semibold text-[#1d1d1f]">Confirmation</h1>
      <p class="text-[#86868b] mt-1">Review your documents and message before sending.</p>
    </div>

    <div class="flex gap-6 h-[calc(100vh-260px)]">
      <div class="w-1/2 space-y-6 overflow-y-auto pr-2">
        <div class="bg-white border border-[#e5e5e7] rounded-2xl p-5 shadow-sm">
          <h2 class="text-base font-medium text-[#1d1d1f] mb-4">Schedule Delivery</h2>
          <div class="space-y-4">
            <div class="space-y-2">
              <label for="scheduleDate" class="block text-sm font-medium text-[#86868b]">Date</label>
              <div class="relative date-picker-field">
                <input id="scheduleDate" class="w-full bg-white border border-[#e5e5e7] rounded-xl py-3 px-4 text-[#1d1d1f] focus:ring-2 focus:ring-[#0071e3] focus:outline-none transition-all duration-200" type="date">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="calendar-icon lucide lucide-calendar absolute right-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#86868b]">
                  <path d="M8 2v4"></path>
                  <path d="M16 2v4"></path>
                  <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                  <path d="M3 10h18"></path>
                </svg>
              </div>
            </div>

            <div class="space-y-2">
              <label for="scheduleTime" class="block text-sm font-medium text-[#86868b]">Time</label>
              <div class="relative">
                <input id="scheduleTime" class="w-full bg-white border border-[#e5e5e7] rounded-xl py-3 px-4 text-[#1d1d1f] focus:ring-2 focus:ring-[#0071e3] focus:outline-none transition-all duration-200" type="time" value="00:00">
              </div>
            </div>

            <div class="pt-4 mt-2 border-t border-[#f0f0f3]">
              <div class="space-y-3">
                <div class="flex items-center gap-3">
                  <div class="flex items-center h-5">
                    <input id="sendNow" class="h-4 w-4 text-[#0071e3] focus:ring-[#0071e3] border-gray-300 rounded-full" type="radio" name="sendOption">
                  </div>
                  <div class="text-sm">
                    <label for="sendNow" class="font-medium text-[#1d1d1f]">Send immediately</label>
                  </div>
                </div>
                <div class="flex items-center gap-3">
                  <div class="flex items-center h-5">
                    <input id="waitToSend" class="h-4 w-4 text-[#0071e3] focus:ring-[#0071e3] border-gray-300 rounded-full" type="radio" checked="" name="sendOption"></div>
                  <div class="text-sm">
                    <label for="waitToSend" class="font-medium text-[#1d1d1f]">Wait to send</label>
                    <p class="text-xs text-[#86868b]">The document will be created but not sent until you manually send it</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white border border-[#e5e5e7] rounded-2xl p-5 shadow-sm">
          <h2 class="text-base font-medium text-[#1d1d1f] mb-4">Patient Information</h2>
          <div class="space-y-3">
            <div class="bg-gradient-to-r from-white to-[#f9f9fb] rounded-xl shadow-md border border-[#e5e5e7] flex items-center overflow-hidden transition-all duration-300 hover:shadow-lg group">
              <div class="px-5 py-3 text-right">
                <div class="font-semibold text-[#1d1d1f] mb-1 group-hover:text-[#ff9f66] transition-colors duration-200 document-patient-name"><%= signature_request.patient&.full_name %></div>
                <div class="text-xs space-y-0.5 text-[#86868b]">
                  <div class="flex items-center justify-end gap-1.5">
                    <span class="document-patient-dob">DOB: <%= signature_request.patient&.date_of_birth&.strftime('%m/%d/%Y') %></span>
                    <svg class="w-3 h-3 text-[#86868b]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17 3h4a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h4V1h2v2h6V1h2v2zm-2 2H9v2H7V5H4v4h16V5h-3v2h-2V5zm5 6H4v8h16v-8z" fill="currentColor"></path>
                    </svg>
                  </div>
                  <div class="flex items-center justify-end gap-1.5">
                    <span class="document-patient-phone"><%= signature_request.patient&.mobile_phone %></span>
                    <svg class="w-3 h-3 text-[#86868b]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.366 10.682a10.556 10.556 0 0 0 3.952 3.952l.884-1.238a1 1 0 0 1 1.294-.296 11.422 11.422 0 0 0 4.583 1.364 1 1 0 0 1 .921.997v4.462a1 1 0 0 1-.898.995c-.53.055-1.064.082-1.602.082C9.94 21 3 14.06 3 5.5c0-.538.027-1.072.082-1.602A1 1 0 0 1 4.077 3h4.462a1 1 0 0 1 .997.921A11.422 11.422 0 0 0 10.9 8.504a1 1 0 0 1-.296 1.294l-1.238.884zm-2.522-.657l1.9-1.357A13.41 13.41 0 0 1 7.647 5H5.01c-.006.166-.009.333-.009.5C5 12.956 11.044 19 18.5 19c.167 0 .334-.003.5-.01v-2.637a13.41 13.41 0 0 1-3.668-1.097l-1.357 1.9a12.442 12.442 0 0 1-1.588-.75l-.058-.033a12.556 12.556 0 0 1-4.702-4.702l-.033-.058a12.442 12.442 0 0 1-.75-1.588z" fill="currentColor"></path>
                    </svg>
                  </div>
                  <div class="flex items-center justify-end gap-1.5">
                    <span class="document-patient-email"><%= signature_request.patient&.email %></span>
                    <svg class="w-3 h-3 text-[#86868b]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm17 4.238l-7.928 7.1L4 7.216V19h16V7.238zM4.511 5l7.55 6.662L19.502 5H4.511z" fill="currentColor"></path>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="document-patient-initials w-12 h-12 rounded-full bg-gradient-to-br from-[#e6f0ff] to-[#d1e3ff] flex items-center justify-center text-[#3b82f6] font-semibold text-lg mr-4 shadow-inner border border-[#d1e3ff] group-hover:scale-110 transition-transform duration-300">
                <%= signature_request.patient&.first_name&.first&.capitalize %><%= signature_request.patient&.last_name&.first&.capitalize %>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white border border-[#e5e5e7] rounded-2xl p-5 shadow-sm message-preview">
          <h2 class="text-base font-medium text-[#1d1d1f] mb-4">Message Preview</h2>
          <div class="text-sm text-[#3a3a3c] leading-relaxed message-preview-content"></div>
        </div>
      </div>

      <div class="w-1/2 space-y-6 overflow-y-auto pl-2">
        <% @templates.each_with_index do |template, index| %>
          <div data-type="consent" data-id="<%= template.id %>" class="bg-white border border-[#e5e5e7] rounded-2xl p-4 shadow-sm content-preview">
            <h2 class="text-base font-medium text-[#1d1d1f] mb-2"><%= template.name %></h2>
            <div class="w-full h-64 rounded-lg pt-4 preview-body overflow-y-auto">Document Preview</div>
          </div>
        <% end %>

        <% @treatment_plans.each_with_index do |template, index| %>
          <div data-type="plan" data-id="<%= template.id %>" class="bg-white border border-[#e5e5e7] rounded-2xl p-4 shadow-sm content-preview">
            <h2 class="text-base font-medium text-[#1d1d1f] mb-2"><%= template.title %></h2>
            <div class="preview-body w-full h-130 bg-[#f5f5f7] rounded-lg flex items-center justify-center text-[#86868b]">
              Document Preview
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
