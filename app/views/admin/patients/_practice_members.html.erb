<div class="assigned-practices-members" data-practices-editing="false">
  <% if patient.practices.any? %>
    <div class="grid grid-cols-4 gap-4 mt-2">
      <% patient.practices.each do |practice| %>
        <div class="flex flex-col items-center practice-container">
          <div class="relative practice-wrapper">
            <div class="practice-avatar">
              <span class="relative flex shrink-0 overflow-hidden rounded-full border border-gray-100 shadow-sm h-[50px] w-[50px]">
                <% if practice.logo.attached? %>
                  <%= image_tag practice.logo.url, class: "aspect-square h-full w-full object-cover", alt: practice.name %>
                <% else %>
                  <div class="bg-blue-100 h-full w-full rounded-full flex items-center justify-center">
                    <span class="text-blue-600 text-[14px] font-medium"><%= practice.name.first.upcase %></span>
                  </div>
                <% end %>
              </span>
            </div>
            <button class="absolute -top-1 -right-1 hidden remove-practice bg-red-500 rounded-full w-5 h-5 flex items-center justify-center shadow-sm" 
                    data-practice-id="<%= practice.id %>" 
                    data-patient-id="<%= patient.id %>">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>
          <span class="text-[13px] text-center text-gray-700 font-medium mt-1.5"><%= practice.name %></span>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="py-4 text-center text-gray-500">
      <p class="text-sm">No practices assigned</p>
      <p class="text-xs mt-1">Click the + button to assign practices</p>
    </div>
  <% end %>
</div>
