<div class="flex flex-col h-screen bg-[#f5f5f7]">
  <div class="flex-shrink-0 bg-white border-b border-gray-200">
    <div class="container mx-auto px-4 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold">Patient List</h1>
          <div class="ml-4">
            <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
              <a href="<%= admin_patients_path(archived: false) %>"
                 class="filter-btn min-w-[36px] <%= params[:archived] != 'true' ? 'pl-3 pr-4' : 'px-3' %> h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden <%= params[:archived] != 'true' ? 'text-blue-800' : 'bg-white text-gray-600 hover:bg-gray-50' %>"
                 <%= params[:archived] != 'true' ? 'style="background: linear-gradient(to right, #bfdbfe, #93c5fd);"'.html_safe : '' %>>
                <i class="fa-light fa-user h-4 w-4 <%= params[:archived] != 'true' ? 'text-blue-700' : '' %>"></i>
                <span class="tab-text text-xs font-medium tracking-wide <%= params[:archived] != 'true' ? 'ml-1.5' : 'opacity-0 w-0' %> transition-all duration-300">Active</span>
              </a>
              <a href="<%= admin_patients_path(archived: true) %>"
                 class="filter-btn min-w-[36px] <%= params[:archived] == 'true' ? 'pl-3 pr-4' : 'px-3' %> h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden <%= params[:archived] == 'true' ? 'text-red-800' : 'bg-white text-gray-600 hover:bg-gray-50' %>"
                 <%= params[:archived] == 'true' ? 'style="background: linear-gradient(to right, #fecaca, #fca5a5);"'.html_safe : '' %>>
                <i class="fa-light fa-archive h-4 w-4 <%= params[:archived] == 'true' ? 'text-red-700' : '' %>"></i>
                <span class="tab-text text-xs font-medium tracking-wide <%= params[:archived] == 'true' ? 'ml-1.5' : 'opacity-0 w-0' %> transition-all duration-300">Archived</span>
              </a>
            </div>
          </div>
        </div>
        <div class="flex items-center">
          <%= form_tag create_temporary_admin_patients_path, method: :post, class: "inline-block" do %>
            <button type="submit" class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border hover:text-accent-foreground py-2 h-10 px-4 bg-blue-100 hover:bg-blue-200 text-blue-800 border-blue-200 rounded-lg shadow-sm transition-all duration-200 text-xs font-medium">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-plus w-4 h-4 mr-1">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <line x1="19" x2="19" y1="8" y2="14"></line>
                <line x1="22" x2="16" y1="11" y2="11"></line>
              </svg>
              Add Patient
            </button>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="flex-1 overflow-auto p-4">
    <div class="container mx-auto space-y-3">
    <div class="md:grid grid-cols-7 items-center gap-x-6 px-5 py-2 mb-3">
      <div class="text-xs font-normal text-gray-400 uppercase tracking-wider">Name</div>

      <div class="text-xs font-normal text-gray-400 uppercase tracking-wider">Date of Birth</div>
      <div class="text-xs font-normal text-gray-400 uppercase tracking-wider">Email</div>
      <div class="text-xs font-normal text-gray-400 uppercase tracking-wider">Mobile Phone</div>
      <div class="text-xs font-normal text-gray-400 uppercase tracking-wider">Postcode</div>
      <div class="text-xs font-normal text-gray-400 uppercase tracking-wider">Practices</div>

      <div class="text-xs font-normal text-gray-400 uppercase tracking-wider text-right sr-only">Actions</div>
    </div>
      <% if @patients.empty? %>
        <div class="bg-white p-8 rounded-lg shadow text-center">
          <p class="text-gray-500 text-lg">No patients found</p>
        </div>
      <% else %>
        <% @patients.each do |patient| %>
          <div class="group grid grid-cols-7 items-center gap-x-6 bg-white shadow-sm rounded-xl p-4 hover:shadow-lg transition-all duration-300 ease-in-out border border-gray-200/75 hover:border-gray-300">
            <div class="flex flex-col md:flex-row md:items-center">
              <span class="text-sm font-medium text-gray-800 group-hover:text-gray-900">
                <%= link_to admin_patient_path(patient) do %>
                  <%= patient.first_name %> <%= patient.last_name %>
                <% end %>
              </span>
            </div>

            <div class="text-sm text-gray-600">
              <%= patient.date_of_birth&.strftime("%d/%m/%Y") || "--/--/----" %>
            </div>

            <div class="text-sm text-gray-600">
              <%= patient.email || "No Email" %>
            </div>

            <div class="text-sm text-gray-600">
              <%= patient.mobile_phone || "No Phone" %>
            </div>

            <div class="text-sm text-gray-600">
              <%= patient.postcode || "No Postcode" %>
            </div>
            <div class="text-sm text-gray-600">
              <%= patient.practices.map(&:name).join(", ") %>
            </div>

            <div class="mt-2 md:mt-0 flex flex-col space-y-2 md:space-y-0 md:flex-row md:justify-end md:space-x-1.5 items-stretch md:items-center">
              <a href="<%= edit_admin_patient_path(patient) %>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 rounded-md text-xs text-gray-500 hover:text-blue-600 hover:bg-blue-50 h-auto px-2.5 py-1.5 group-hover:opacity-100 md:opacity-0 transition-opacity duration-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pen-line h-3.5 w-3.5">
                  <path d="M12 20h9"></path>
                  <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z"></path>
                </svg>
                <span class="ml-1.5 md:hidden lg:inline">Edit</span>
              </a>

              <% if !params[:archived] == 'true' %>
                <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 rounded-md text-xs text-red-500 hover:text-red-700 hover:bg-red-50 h-auto px-2.5 py-1.5 group-hover:opacity-100 md:opacity-0 transition-opacity duration-200">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash2 h-3.5 w-3.5">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                    <line x1="10" x2="10" y1="11" y2="17"></line>
                    <line x1="14" x2="14" y1="11" y2="17"></line>
                  </svg>
                  <span class="ml-1.5 md:hidden lg:inline">Delete</span>
                </button>
              <% end %>

              <a href="<%= admin_patient_path(patient) %>" class="inline-block">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 text-gray-300 group-hover:text-gray-400 md:group-hover:opacity-0 transition-opacity duration-200">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </a>
            </div>
          </div>
        <% end %>

        <div class="mt-4 flex justify-between items-center bg-white p-4 rounded-xl shadow-sm border border-gray-200/75">
          <div class="text-sm text-gray-600">
            Showing <%= @patients.offset_value + 1 %> to <%= [@patients.offset_value + @patients.length, @patients.total_entries].min %> of <%= @patients.total_entries %> patients
          </div>

          <div class="pagination-container">
            <%= will_paginate @patients,
              class: "flex items-center space-x-2",
              inner_window: 1,
              outer_window: 0,
              previous_label: "Previous",
              next_label: "Next",
              page_links: true,
              link_options: { class: "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 border-gray-200" },
              params: params.permit(:page, :search) %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
