<%= form_with(model: [:admin_general_settings, @template], local: true) do |form| %>
  <div class="card ms-3 ai-notes" style="margin-bottom: 1rem !important;">
    <%= form.label :name, 'Name', class: "fw-normal mb-2", style: 'font-size: 14px;' %>
    <%= form.text_field :name, class: "form-control fw-normal mb-3", style: 'font-size: 14px;' %>

    <%= form.label :practice_id, "Practice", class: 'form-label' %>
    <%= form.select :practice_id,
                    options_from_collection_for_select(
                      @practices,
                      :id,
                      :name,
                      form.object.practice_id.presence || Current.practice_id
                    ),
                    {},
                    { class: 'form-control mb-3', required: true }
    %>

    <%= form.label :template_type, class: "fw-normal mb-2", style: "font-size: 14px;" %>
    <%= form.select :template_type, document_template_type_options_for_select, {}, class: "form-control fw-normal mb-3" %>

    <div class="mb-4">
      <%= form.text_field :text, class: 'tinymce_editor ai-textarea',
                          data: {
                            mergetags_list: [
                              {value: 'patient.first_name', title: 'First Name'},
                              {value: 'patient.last_name', title: 'Last Name'},
                              {value: 'patient.email', title: 'Email'}],
                            enable_signature_fields: true
                          } %>
    </div>

    <%= form.label :treatment_ids, 'Linked Treatments', class: "fw-normal mb-2", style: 'font-size: 14px;' %>
    <%= form.collection_select :treatment_ids, @treatments, :id, :patient_friendly_name, {}, { multiple: true, class: "form-control select2-multiple", style: 'font-size: 14px; width: 100%;' } %>

    <div class="mt-3 d-flex justify-content-between align-items-start">
      <div class="d-flex justify-content-start align-items-center">
        <div class="d-flex align-items-center" style="padding: 14px 20px; border-radius: 8px; border: 1px solid #D8D8D8; background-color: #ffffff">
          <div class="icon">
            <span class="me-2">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 11V9H19V7C18.9984 6.47005 18.7872 5.96227 18.4125 5.58753C18.0377 5.2128 17.5299 5.00158 17 5H15V3H13V5H11V3H9V5H7C6.47005 5.00158 5.96227 5.2128 5.58753 5.58753C5.2128 5.96227 5.00158 6.47005 5 7V9H3V11H5V13H3V15H5V17C5.00158 17.5299 5.2128 18.0377 5.58753 18.4125C5.96227 18.7872 6.47005 18.9984 7 19H9V21H11V19H13V21H15V19H17C17.5299 18.9984 18.0377 18.7872 18.4125 18.4125C18.7872 18.0377 18.9984 17.5299 19 17V15H21V13H19V11H21ZM17 17H7V7H17V17Z" fill="#3F3F3F"/>
              <path d="M11.3595 8H10.0145L8.00452 16H9.03152L9.49552 14.125H11.8115L12.2635 16H13.3255L11.3595 8ZM9.63052 13.324L10.6485 8.95H10.6945L11.6775 13.324H9.63052ZM14.2425 8H15.2425V16H14.2425V8Z" fill="#6D6B6B"/>
              </svg>
            </span>
            <span style="font-weight: normal; font-size: 12px;">Make it</span>
          </div>

          <div class="d-flex flex-wrap gap-2 ms-3 ai-styles">
            <button type="button" class="btn btn-sm ai-button">Empathetic</button>
            <button type="button" class="btn btn-sm ai-button">Professional</button>
            <button type="button" class="btn btn-sm ai-button">Friendly</button>
            <button type="button" class="btn btn-sm ai-button">Apologetic</button>

            <div class="vr mx-2"></div>

            <button type="button" class="btn btn-sm ai-button">Simplify</button>
            <button type="button" class="btn btn-sm ai-button">Improve it</button>

            <div class="vr mx-2"></div>

            <button type="button" class="btn btn-sm" style="background-color: #ffffff; border: 1px solid #2A4558; border-radius: 30px;">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14.5846 7.5013C14.5846 3.58918 11.4134 0.417969 7.50131 0.417969C3.58918 0.417969 0.417975 3.58918 0.417975 7.5013C0.416503 8.63318 0.687391 9.7488 1.20777 10.754L0.451266 13.4619C0.40907 13.6131 0.40783 13.7728 0.447673 13.9247C0.487516 14.0765 0.567011 14.215 0.678011 14.326C0.789011 14.437 0.927525 14.5165 1.07936 14.5564C1.2312 14.5962 1.3909 14.595 1.5421 14.5528L4.25218 13.797C5.42312 14.4027 6.74036 14.6682 8.05452 14.5634C7.75888 14.2518 7.50829 13.9004 7.31006 13.5193C6.35061 13.4907 5.41212 13.2317 4.57377 12.7642L4.38252 12.658L1.5591 13.4449L2.34747 10.6229L2.24052 10.4317C1.74055 9.53603 1.47885 8.52704 1.48047 7.5013C1.47872 5.91989 2.09922 4.40127 3.20791 3.27358C4.3166 2.14589 5.82445 1.49967 7.40567 1.47455C8.98689 1.44943 10.5145 2.04742 11.6585 3.13933C12.8024 4.23124 13.4708 5.72938 13.5193 7.31005C13.9018 7.50909 14.2531 7.76055 14.5634 8.0538C14.5776 7.872 14.5846 7.68783 14.5846 7.5013ZM8.09418 10.669C8.27921 10.6232 8.45309 10.5405 8.60541 10.4259C8.75773 10.3113 8.88536 10.1672 8.98066 10.0021C9.07596 9.83704 9.13697 9.65443 9.16004 9.46523C9.1831 9.27602 9.16775 9.0841 9.11489 8.90097L8.97464 8.41434C9.28679 8.14258 9.64173 7.9243 10.0251 7.76834L10.3786 8.13951C10.5108 8.27876 10.6701 8.38965 10.8465 8.46545C11.023 8.54124 11.2131 8.58037 11.4051 8.58043C11.5972 8.5805 11.7873 8.54151 11.9638 8.46583C12.1403 8.39016 12.2996 8.27937 12.432 8.14022L12.7812 7.7733C13.1653 7.93113 13.5205 8.15158 13.8324 8.42568L13.7006 8.87263C13.6463 9.05684 13.63 9.25016 13.6528 9.44085C13.6755 9.63155 13.7367 9.81563 13.8328 9.98192C13.9288 10.1482 14.0577 10.2932 14.2116 10.4082C14.3654 10.5231 14.541 10.6056 14.7277 10.6506L15.1095 10.7426C15.1791 11.1666 15.1805 11.5989 15.1138 12.0233L14.7001 12.1253C14.5151 12.1712 14.3413 12.2539 14.1891 12.3685C14.0368 12.4831 13.9093 12.6273 13.8141 12.7924C13.7188 12.9574 13.6579 13.14 13.6349 13.3292C13.6118 13.5183 13.6272 13.7102 13.6801 13.8933L13.8196 14.3792C13.5075 14.6512 13.1526 14.8698 12.7692 15.0259L12.4164 14.6541C12.2841 14.5147 12.1248 14.4038 11.9482 14.328C11.7716 14.2522 11.5815 14.2131 11.3893 14.2131C11.1972 14.2131 11.0071 14.2522 10.8305 14.328C10.6539 14.4038 10.4946 14.5147 10.3623 14.6541L10.0131 15.0217C9.62895 14.8636 9.27374 14.6429 8.96189 14.3686L9.09435 13.9216C9.14866 13.7374 9.16495 13.5441 9.14224 13.3534C9.11952 13.1627 9.05827 12.9786 8.9622 12.8123C8.86614 12.6461 8.73727 12.501 8.58342 12.3861C8.42958 12.2712 8.25397 12.1887 8.06727 12.1437L7.68477 12.0516C7.61522 11.6277 7.61379 11.1954 7.68052 10.771L8.09418 10.669ZM12.4242 11.3971C12.4242 10.8106 11.9638 10.3346 11.3971 10.3346C10.8305 10.3346 10.3701 10.8106 10.3701 11.3971C10.3701 11.9836 10.8305 12.4596 11.3971 12.4596C11.9638 12.4596 12.4242 11.9836 12.4242 11.3971Z" fill="black"/>
              </svg>
            </button>
          </div>
        </div>
        <div class="ai-input-btns ms-3" style="display: flex;justify-content: end;align-items: center;padding: 0.5rem;">
          <button type="button" class="btn border p-0 rounded-pill ai-record" style="
          width: 72px;
            height: 72px;
            font-size: 12px;
            background: #FEA8A8CC !important;
          ">
            <svg width="28" height="42" viewBox="0 0 28 42" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14.0003 28.4268C16.1566 28.424 18.2238 27.5662 19.7486 26.0414C21.2734 24.5166 22.1312 22.4494 22.134 20.2931V8.97653C22.134 6.81933 21.2771 4.75047 19.7517 3.2251C18.2263 1.69972 16.1575 0.842773 14.0003 0.842773C11.8431 0.842773 9.77422 1.69972 8.24884 3.2251C6.72347 4.75047 5.86652 6.81933 5.86652 8.97653V20.2931C5.86933 22.4494 6.72717 24.5166 8.25194 26.0414C9.77671 27.5662 11.8439 28.424 14.0003 28.4268ZM7.98837 8.97653C7.98837 7.38208 8.62176 5.85292 9.74922 4.72547C10.8767 3.59802 12.4058 2.96462 14.0003 2.96462C15.5947 2.96462 17.1239 3.59802 18.2513 4.72547C19.3788 5.85292 20.0122 7.38208 20.0122 8.97653V20.2931C20.0122 21.8875 19.3788 23.4167 18.2513 24.5441C17.1239 25.6716 15.5947 26.305 14.0003 26.305C12.4058 26.305 10.8767 25.6716 9.74922 24.5441C8.62176 23.4167 7.98837 21.8875 7.98837 20.2931V8.97653ZM15.0612 34.0409V40.097C15.0612 40.3784 14.9494 40.6482 14.7505 40.8472C14.5515 41.0462 14.2817 41.1579 14.0003 41.1579C13.7189 41.1579 13.4491 41.0462 13.2501 40.8472C13.0511 40.6482 12.9394 40.3784 12.9394 40.097V34.0409C9.47736 33.7701 6.24451 32.2048 3.88506 29.6569C1.5256 27.109 0.212795 23.7656 0.208252 20.2931C0.208252 20.0117 0.320028 19.7418 0.51899 19.5429C0.717952 19.3439 0.987802 19.2321 1.26918 19.2321C1.55055 19.2321 1.8204 19.3439 2.01936 19.5429C2.21833 19.7418 2.3301 20.0117 2.3301 20.2931C2.3301 23.3882 3.55964 26.3565 5.74822 28.5451C7.9368 30.7337 10.9052 31.9632 14.0003 31.9632C17.0954 31.9632 20.0638 30.7337 22.2523 28.5451C24.4409 26.3565 25.6705 23.3882 25.6705 20.2931C25.6705 20.0117 25.7822 19.7418 25.9812 19.5429C26.1802 19.3439 26.45 19.2321 26.7314 19.2321C27.0128 19.2321 27.2826 19.3439 27.4816 19.5429C27.6805 19.7418 27.7923 20.0117 27.7923 20.2931C27.7878 23.7656 26.475 27.109 24.1155 29.6569C21.756 32.2048 18.5232 33.7701 15.0612 34.0409Z" fill="black"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="fixed-footer justify-content-end show play-anim">
    <%= link_to 'Cancel', admin_general_settings_document_templates_path, class: 'cool-gray-btn'%>
    <%= form.submit 'Save', name: 'commit', class: 'btn btn-primary footer-btn orangebtn' %>
  </div>
<% end %>
