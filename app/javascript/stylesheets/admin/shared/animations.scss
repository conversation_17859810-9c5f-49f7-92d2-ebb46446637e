@keyframes shake-animation {
  0% {
    transform: translate(0) scale(1);
  }
  25% {
    transform: translate(-3px, 0) scale(1.05);
  }
  50% {
    transform: translate(0, 0) scale(1);
  }
  75% {
    transform: translate(3px, 0) scale(1.05);
  }
  100% {
    transform: translate(0) scale(1);
  }
}

.shake-animation {
  animation: shake-animation 1.2s infinite ease-in-out;
  display: inline-block;
}

// Fade out animation when removing a team member
.team-member-container.fade-out,
.practice-container.fade-out {
  opacity: 0;
  transform: scale(0.8);
  transition: opacity 0.3s ease, transform 0.3s ease;
  pointer-events: none;
}

// Style adjustments for edit mode
.assigned-team-members[data-team-editing="true"] {
  .team-member-avatar {
    position: relative;
    z-index: 1;
  }
  
  .remove-team-member {
    z-index: 2;
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
}
