@import 'trix/dist/trix';
@import "select2/dist/css/select2.css";
@import "toastr/toastr";
@import 'tippy.js/dist/tippy.css';
@import 'tippy.js/themes/light.css';
@import "animate.css/animate.css";
@import "dropzone/dist/dropzone.css";
@import 'flatpickr/dist/flatpickr.min.css';
@import "air-datepicker/air-datepicker.css";
@import "slick-carousel/slick/slick";
@import "slick-carousel/slick/slick-theme";

@import "font-awesome-pro";
@import "font-awesome-pro/brands";
@import "font-awesome-pro/solid";
@import "font-awesome-pro/regular";
@import "font-awesome-pro/light";
@import "font-awesome-pro/thin";
@import "font-awesome-pro/duotone";
@import "font-awesome-pro/sharp-solid";
@import "font-awesome-pro/sharp-regular";
@import "font-awesome-pro/sharp-light";
@import "font-awesome-pro/sharp-thin";
@import "font-awesome-pro/sharp-duotone-solid";
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700;800');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200');
$border-color:     #fff;

@import "./_config";
@import "./shared/*";

body {
  font-size: 14px;
  font-family: "Poppins", sans-serif;
}

// Custom styles
@import "./navbar/navbar";
@import "./navbar/privacy_screen";
@import "./general_settings/*";
@import "./patients/charting/*";
@import "./patients/perio_exams/*";
@import "./patients/account/*";
@import "./patients/information";
@import "./patients/medical_histories/*";
@import "./patients/tabs";
@import "./patients/assets";
@import "./patients/address";
@import "./patients/pinned_notes";
@import "./conversations/*";
@import "./actions/*";
@import "./patients/payments";
@import "./patients/invoices/*";
@import "./patients/charting_appointments";
@import "./treatment_plans/*";
@import "./treatment_plan_options/*";
@import "./calendar_bookings/**/*";
@import "./treatment_plan_estimates/*";
@import "./hr_management/*";
@import "./lab_works/*";
@import "./lab_dockets/*";
@import "./signature_requests/*";
@import "./prescriptions/*";
@import "./reports/*";
@import "./prescriptions/*";
@import "./dashboards/*";
@import "./crm/*";
@import "./crm/custom_fields";

//.tippy-box[data-theme~='light'] {
//  border-radius: 14px;
//  background-color: black;
//  color: white;
//  border: 1px solid #848484;
//  padding: 10px 20px 10px 20px;
//}