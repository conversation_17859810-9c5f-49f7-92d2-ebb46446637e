$(document).ready(function () {
  if ($('.signature-document-form').length === 0) return;

  const isEditMode = document.querySelector('.signature-document-form')?.dataset.editMode === 'true';

  let currentStep = isEditMode ? 2 : 1;
  const totalSteps = 4;

  const filterButtons = document.querySelectorAll('.category-filter');
  const templates = document.querySelectorAll('.doc-template');
  const selectedDocuments = new Set();

  let currentCategory = null;

  initializeGeneralInformationModal();
  initSelect2Patients();
  initRadiobuttons();
  initializeStepNavigation();
  initializeDocumentSelection();
  initializeCategoryFilter();
  initializeSearchFilter();

  function initializeDocumentSelection() {
    $('.doc-template').on('click', function () {
      const $card = $(this);
      const docId = $card.data('id');
      const docName = $card.data('name');
      const docType = $card.data('type');

      if (currentCategory === "Treatment plan") {
        clearSelections();
      }

      if (selectedDocuments.has(docId)) {
        selectedDocuments.delete(docId);
        unhighlightCard($card)

        $(`#selected-documents-list [data-id="${docId}"]`).remove();
      } else {
        selectedDocuments.add(docId);
        $card.addClass('ring-2 ring-[#a8e6cf] shadow-[0_0_15px_rgba(168,230,207,0.25)] scale-[1.02]');

        const $iconContainer = $card.find('.self-end.mb-2');
        if ($iconContainer.length) {
          $iconContainer.html(`
            <div class="w-6 h-6 rounded-full bg-[#a8e6cf] flex items-center justify-center shadow-sm">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-3.5 w-3.5 text-white">
                <path d="M20 6 9 17l-5-5"></path>
              </svg>
            </div>
          `);
        }
        addDocumentToList(docId, docName, docType);
      }

      if (!isEditMode) {
        if (currentCategory === "Treatment plan") {
          updateHiddenField('signature_request_treatment_plan_templates');
        } else {
          updateHiddenField('signature_request_templates');
        }
      }

      toggleNextButton();
    });

    $(document).on('click', '.remove-document', function (e) {
      e.stopPropagation();

      const docId = $(this).data('id');
      selectedDocuments.delete(docId);

      const $card = $(`.doc-template[data-id="${docId}"]`);
      unhighlightCard($card);
      removeDocumentFromList(docId);

      if (currentCategory === "Treatment plan") {
        updateHiddenField('signature_request_treatment_plan_templates');
      } else {
        updateHiddenField('signature_request_templates');
      }
      toggleNextButton();
    });

    // if (currentCategory === "Treatment plan") {
    //   updateHiddenField('signature_request_treatment_plan_templates');
    // } else {
    //   updateHiddenField('signature_request_templates');
    // }
    toggleNextButton();
  }

  function unhighlightCard($card) {
    const $icon = $card.find('.self-end.mb-2');
    $card.removeClass('ring-2 ring-[#a8e6cf] shadow-[0_0_15px_rgba(168,230,207,0.25)] scale-[1.02]');
    $icon.html('<div class="px-3 py-1 rounded-full bg-[#ffebee] text-[#f44336] text-xs font-medium">REQUIRED</div>');
  }

  function addDocumentToList(docId, docName, docType) {
    const $selectedList = $('#selected-documents-list');

    // Create the selected document item
    const documentItem = `
      <div class="group flex items-center justify-between py-3 px-4 bg-white rounded-xl border border-[#f2f2f7] shadow-sm mb-2 transition-all duration-200 hover:shadow-md hover:border-[#e5e5e7]" data-id="${docId}">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-10 bg-gradient-to-b from-[#f8f8fa] to-[#f5f5f7] rounded-md flex items-center justify-center relative overflow-hidden shadow-sm border border-[#e5e5e7]">
            <div class="absolute top-1/4 left-[20%] right-[20%] flex flex-col gap-[3px]">
              <div class="h-[1.5px] bg-[#e5e5e7ea] rounded-full w-full"></div>
              <div class="h-[1.5px] bg-[#e5e5e7ea] rounded-full w-[80%]"></div>
              <div class="h-[1.5px] bg-[#e5e5e7ea] rounded-full w-[60%]"></div>
            </div>
          </div>
          <div class="flex flex-col">
            <span class="text-sm font-medium text-[#1d1d1f]">${docName}</span>
            <span class="text-xs text-[#86868b]">PDF</span>
          </div>
        </div>
        <button type="button" class="remove-document w-7 h-7 rounded-full flex items-center justify-center text-[#8e8e93] hover:text-[#ff3b30] hover:bg-[#ffebee] transition-all duration-200" data-id="${docId}">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x h-3.5 w-3.5">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>
    `;

    // Add to the list if it doesn't already exist
    if ($selectedList.find(`[data-document-id="${docId}"]`).length === 0) {
      $selectedList.append(documentItem);
    }
  }

  function removeDocumentFromList(id) {
    $(`#selected-documents-list [data-id="${id}"]`).remove();
  }

  function updateHiddenField(field) {
    $(`#${field}`).val([...selectedDocuments].join(','));
    // $('#signature_request_templates').val([...selectedDocuments].join(','));
  }

  function toggleNextButton() {
    const isEmpty = selectedDocuments.size === 0;
    const $btn = $('#document-selection-next-button');

    $btn.prop('disabled', isEmpty);
    $btn.toggleClass('opacity-50 cursor-not-allowed', isEmpty);
  }

  function initializeStepNavigation() {
    const initialStep = isEditMode ? 2 : 1;
    goToStep(initialStep);

    if (isEditMode) {
      handleDocumentCustomization();
    }

    $('.next-step-button').on('click', function () {
      if (currentStep < totalSteps) goToStep(currentStep + 1);

      $('.back-step-button').removeClass('hidden');

      if (currentStep === 2) {
        handleDocumentCustomization()

        if (isEditMode) {
          $('.back-step-button').addClass('hidden');
        }
      }

      if (currentStep === 4) {
        handleConfirmation()
        handleScheduledDelivery()
      }

      if (currentStep === 4) {
        $('.next-step-button').addClass('hidden');
        $('.save-button').removeClass('hidden');
      } else {
        $('.next-step-button').removeClass('hidden');
        $('.save-button').addClass('hidden');
      }
    });

    $('.back-step-button').on('click', function () {
      if (currentStep > 1) goToStep(currentStep - 1);

      if (currentStep === 1) {
        $('.back-step-button').addClass('hidden');
      }

      if (currentStep === 2) {
        handleDocumentCustomization()

        if (isEditMode) {
          $('.back-step-button').addClass('hidden');
        }
      }

      if (currentStep === 4) {
        handleConfirmation()
        handleScheduledDelivery()
      }

      if (currentStep === 4) {
        $('.next-step-button').addClass('hidden');
        $('.save-button').removeClass('hidden');
      } else {
        $('.next-step-button').removeClass('hidden');
        $('.save-button').addClass('hidden');
      }
    });

    $('.step-button').on('click', function () {
      const step = parseInt($(this).data('step'));
      if (isEditMode && step === 1) return;

      if (!isNaN(step) && step <= currentStep + 1) goToStep(step);

      if (step === 1) {
        $('.back-step-button').addClass('hidden');
      } else {
        $('.back-step-button').removeClass('hidden');
      }

      if (step === 2) {
        handleDocumentCustomization()

        if (isEditMode) {
          $('.back-step-button').addClass('hidden');
        }
      }

      if (step === 4) {
        handleConfirmation()
        handleScheduledDelivery()
      }

      if (currentStep === 4) {
        $('.next-step-button').addClass('hidden');
        $('.save-button').removeClass('hidden');
      } else {
        $('.next-step-button').removeClass('hidden');
        $('.save-button').addClass('hidden');
      }
    });
  }

  function goToStep(step) {
    currentStep = step;

    $('.form-section').hide();
    $(`#document-selection-section, #customization-section, #message-section, #confirmation-section`).hide();

    switch (step) {
      case 1: $('#document-selection-section').show(); break;
      case 2: $('#customization-section').show(); break;
      case 3: $('#message-section').show(); break;
      case 4: $('#confirmation-section').show(); break;
    }

    updateProgressBar();
    updateStepButtons();
    window.scrollTo(0, 0);
  }

  function updateProgressBar() {
    const completedPercent = ((currentStep - 1) / totalSteps) * 100;
    const currentPercent = (currentStep / totalSteps) * 100;

    $('.progress-bar-completed').css('width', `${completedPercent}%`);
    $('.progress-bar-current').css('width', `${currentPercent}%`);
  }

  function updateStepButtons() {
    $('.step-button').each(function (index) {
      const step = index + 1;
      const $button = $(this);

      if (step < currentStep) {
        // Completed step
        $button.addClass('completed').removeClass('current upcoming');
        $button.find('.step-point').addClass('hidden');
      } else if (step === currentStep) {
        // Current step
        $button.addClass('current').removeClass('completed upcoming');
        $button.find('.step-point').removeClass('hidden');
      } else {
        // Upcoming step
        $button.addClass('upcoming').removeClass('completed current');
        $button.find('.step-point').addClass('hidden');
      }
    });
  }

  function initializeCategoryFilter() {
    // Set default active button
    const defaultButton = document.querySelector('#patient_consents_btn');
    if (defaultButton) activateButton(defaultButton);

    // Add click event listeners
    filterButtons.forEach(button => {
      button.addEventListener('click', () => {
        clearSelections();
        currentCategory = button.dataset.category;
        activateButton(button)
      });
    });
  }

  function activateButton(targetBtn) {
    filterButtons.forEach(btn => {
      const span = btn.querySelector('span');
      const isActive = btn === targetBtn;
      const color = btn.dataset.expandColor;

      if (isActive) {
        btn.classList.remove('w-10', 'h-10', 'justify-center', 'bg-[#f5f5f7]', 'text-[#3a3a3c]', 'hover:bg-[#e5e5ea]');
        btn.classList.add('px-4', 'py-2', 'shadow-sm', 'border', 'font-medium');
        btn.style.backgroundColor = color;
        btn.style.borderColor = color;
        btn.style.color = '#3a3a3c';
        span.style.display = 'inline';
      } else {
        btn.classList.add('w-10', 'h-10', 'justify-center', 'bg-[#f5f5f7]', 'text-[#3a3a3c]', 'hover:bg-[#e5e5ea]');
        btn.classList.remove('px-4', 'py-2', 'shadow-sm', 'border', 'font-medium');
        btn.style.backgroundColor = '';
        btn.style.borderColor = '';
        btn.style.color = '';
        span.style.display = 'none';
      }
    });

    filterTemplatesByCategory(targetBtn.dataset.category);
  }

  function filterTemplatesByCategory(category) {
    templates.forEach(template => {
      const type = template.dataset.type?.trim();
      if (!type || type === category) {
        template.classList.remove('hidden');
      } else {
        template.classList.add('hidden');
      }
    });

    if (category === 'Treatment plan') {
      const id = $("#signature_request_user_id").val();
      filterTreatmentPlans(id)
    }
  }

  function clearSelections() {
    document.querySelectorAll('.remove-document').forEach(btn => btn.click());
  }

  function initializeSearchFilter() {
    $('.template-search').on('keyup', function () {
      const searchTerm = $(this).val().toLowerCase();

      $('.doc-template').each(function () {
        const name = $(this).data('name').toLowerCase();
        $(this).toggle(name.includes(searchTerm));
      });
    });
  }

  function handleDocumentCustomization() {
    const templateInput = document.querySelector("#signature_request_templates");
    const treatmentPlanInput = document.querySelector("#signature_request_treatment_plan_templates");
    const fallbackBlock = document.querySelector(".not_customize");

    const templateIds = (templateInput?.value || "").split(",").map(id => id.trim()).filter(Boolean);
    const treatmentPlanIds = (treatmentPlanInput?.value || "").split(",").map(id => id.trim()).filter(Boolean);

    const activeBorderClasses = ["border-[#ff9f66]", "shadow-md"];
    const inactiveBorderClasses = ["border-[#e5e5e7]"];

    const activeTextClasses = ["bg-[#fff8f5]"];
    const inactiveTextClasses = ["bg-white"];

    if (templateIds.length === 0 && treatmentPlanIds.length > 0) {
      fallbackBlock?.classList.remove("hidden");
    } else {
      fallbackBlock?.classList.add("hidden");
    }

    document.querySelectorAll(".tab-button, .tab-content").forEach(el => el.classList.add("hidden"));

    templateIds.forEach((id, index) => {
      const button = document.querySelector(`.tab-button[data-id='${id}']`);
      const content = document.querySelector(`.tab-content[data-id='${id}']`);

      if (button && content) {
        button.classList.remove("hidden");
        content.classList.remove("hidden");

        const borderWrapper = button.querySelector(".border");
        const textWrapper = button.querySelector(".tab-title");

        if (index === 0) {
          borderWrapper.classList.remove(...inactiveBorderClasses);
          borderWrapper.classList.add(...activeBorderClasses);

          textWrapper?.classList.remove(...inactiveTextClasses);
          textWrapper?.classList.add(...activeTextClasses);
        } else {
          borderWrapper.classList.remove(...activeBorderClasses);
          borderWrapper.classList.add(...inactiveBorderClasses);

          textWrapper?.classList.remove(...activeTextClasses);
          textWrapper?.classList.add(...inactiveTextClasses);

          content.classList.add("hidden");
        }
      }
    });

    document.querySelectorAll(".tab-button").forEach(button => {
      button.addEventListener("click", () => {
        const id = button.getAttribute("data-id");

        document.querySelectorAll(".tab-content").forEach(c => c.classList.add("hidden"));
        document.querySelectorAll(".tab-button .border").forEach(w => {
          w.classList.remove(...activeBorderClasses);
          w.classList.add(...inactiveBorderClasses);
        });

        document.querySelectorAll(".tab-button .tab-title").forEach(t => {
          t.classList.remove(...activeTextClasses);
          t.classList.add(...inactiveTextClasses);
        });

        const selectedContent = document.querySelector(`.tab-content[data-id='${id}']`);
        const selectedBorder = button.querySelector(".border");
        const selectedText = button.querySelector(".tab-title");

        selectedContent?.classList.remove("hidden");
        selectedBorder?.classList.remove(...inactiveBorderClasses);
        selectedBorder?.classList.add(...activeBorderClasses);
        selectedText?.classList.remove(...inactiveTextClasses);
        selectedText?.classList.add(...activeTextClasses);
      });
    });
  }

  function handleConfirmation() {
    let message = '';
    const messageArea = $('#signature_request_message');
    const editor = tinymce.get(messageArea.attr('id'));

    if (editor) {
      message = editor.getContent({ format: 'text' }).trim();
    }

    if (message) {
      $('.message-preview').show()
      $('.message-preview-content').text(message);
    } else {
      $('.message-preview').hide()
    }

    const templateInput = document.querySelector("#signature_request_templates");
    const treatmentPlanInput = document.querySelector("#signature_request_treatment_plan_templates");
    const templateIds = (templateInput?.value || "").split(",").map(id => id.trim()).filter(Boolean);
    const treatmentPlanIds = (treatmentPlanInput?.value || "").split(",").map(id => id.trim()).filter(Boolean);

    const previews = document.querySelectorAll(".content-preview");

    previews.forEach(preview => {
      const id = preview.getAttribute("data-id");
      const type = preview.getAttribute("data-type");

      if (templateIds.length > 0) {
        if (type === "plan") {
          preview.classList.add("hidden");
        } else {
          if (templateIds.includes(id)) {
            const patientId = $("#signature_request_user_id").val();
            const editor = tinymce.get(`signature_request_template_text_${id}`);
            let originalText = editor.getContent();

            getDocumentPreview(preview, originalText, patientId);
          }

          preview.classList.toggle("hidden", !templateIds.includes(id));
        }
      } else if (treatmentPlanIds.length > 0) {
        if (type === "consent") {
          preview.classList.add("hidden");
        } else {
          if (treatmentPlanIds.includes(id)) {
            getPdfPreview(preview, id);
          }

          preview.classList.toggle("hidden", !treatmentPlanIds.includes(id));
        }
      } else {
        preview.classList.remove("hidden");
      }
    });
  }

  function getDocumentPreview(preview, originalText, patientId) {
    fetch(`/admin/signature_requests/get_document_preview`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: JSON.stringify({
        text: originalText,
        patient_id: patientId
      })
    })
        .then(response => response.json())
        .then(data => {
          const previewBody = preview.querySelector('.preview-body');
          if (previewBody) {
            previewBody.innerHTML = data.text;
          }
        })
        .catch(error => {
          console.error('Failed to get document preview:', error);
        });
  }

  function getPdfPreview(preview, id) {
    fetch('/admin/signature_requests/pdf_preview', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: JSON.stringify({ id })
    })
        .then(response => response.json())
        .then(data => {
          if (data.pdf_url) {
            const previewBody = preview.querySelector('.preview-body');
            if (previewBody) {
              previewBody.innerHTML = `
                        <iframe src="${data.pdf_url}" class="w-full h-full rounded" frameborder="0"></iframe>
                      `;
            }
          } else {
            console.error('PDF not found');
          }
        })
        .catch(error => {
          console.error('Error loading PDF preview:', error);
        });
  }

  function handleScheduledDelivery() {
    const sendNowRadio = document.getElementById("sendNow");
    const waitToSendRadio = document.getElementById("waitToSend");
    const dateInput = document.getElementById("scheduleDate");
    const timeInput = document.getElementById("scheduleTime");
    const sentAtInput = document.getElementById("signature_request_sent_at");

    document.querySelectorAll('.calendar-icon').forEach(icon => {
      icon.addEventListener('click', function() {
        const dateInput = this.closest('.date-picker-field').querySelector('input[type="date"]');
        if (dateInput) {
          dateInput.focus();
          dateInput.showPicker();
        }
      });
    });

    const sentAtValue = sentAtInput.value;

    if (sentAtValue) {
      const parsedDate = new Date(sentAtValue);
      const yyyy = parsedDate.getFullYear();
      const mm = String(parsedDate.getMonth() + 1).padStart(2, '0');
      const dd = String(parsedDate.getDate()).padStart(2, '0');
      const hh = String(parsedDate.getHours()).padStart(2, '0');
      const min = String(parsedDate.getMinutes()).padStart(2, '0');

      dateInput.value = `${yyyy}-${mm}-${dd}`;
      timeInput.value = `${hh}:${min}`;
      waitToSendRadio.checked = true;
    } else {
      sendNowRadio.checked = true;
    }

    updateSentAtField(sendNowRadio, dateInput, timeInput, sentAtInput);

    sendNowRadio.addEventListener("change", function () {
      updateSentAtField(sendNowRadio, dateInput, timeInput, sentAtInput);
    });

    waitToSendRadio.addEventListener("change", function () {
      updateSentAtField(sendNowRadio, dateInput, timeInput, sentAtInput);
    });

    dateInput.addEventListener("change", function () {
      updateSentAtField(sendNowRadio, dateInput, timeInput, sentAtInput);
    });

    timeInput.addEventListener("change", function () {
      updateSentAtField(sendNowRadio, dateInput, timeInput, sentAtInput);
    });
  }

  function updateSentAtField(sendNowRadio, dateInput, timeInput, sentAtInput) {
    if (sendNowRadio.checked) {
      sentAtInput.value = "";
      dateInput.value = "";
      timeInput.value = "";
      dateInput.disabled = true;
      dateInput.classList.remove('bg-white');
      dateInput.classList.add('bg-[#f5f5f7]');
      timeInput.disabled = true;
      timeInput.classList.remove('bg-white');
      timeInput.classList.add('bg-[#f5f5f7]');
    } else {
      dateInput.disabled = false;
      dateInput.classList.remove('bg-[#f5f5f7]');
      dateInput.classList.add('bg-white');
      timeInput.disabled = false;
      timeInput.classList.remove('bg-[#f5f5f7]');
      timeInput.classList.add('bg-white');

      const date = dateInput.value;
      const time = timeInput.value;

      if (date && time) {
        const combined = new Date(`${date}T${time}`);
        sentAtInput.value = combined.toISOString();
      } else {
        sentAtInput.value = "";
      }
    }
  }

  function initializeGeneralInformationModal() {
    const patientModal = document.getElementById("signature-patient-modal");
    const dueDateInput = document.getElementById("signature_request_due_by");
    const today = new Date();
    const oneMonthLater = new Date();
    oneMonthLater.setMonth(today.getMonth() + 1);
    const formatDate = (date) => date.toISOString().split("T")[0];
    dueDateInput.min = formatDate(today);
    dueDateInput.value = formatDate(oneMonthLater);

    if (isEditMode) {
      const raw = document.querySelector('#signature_request_due_by_hidden').value;
      if (raw) {
        const date = new Date(raw);
        dueDateInput.value = date.toISOString().split('T')[0];
      }
    }

    patientModal.classList.remove("hidden");

    const continueButton = document.querySelector('#signature-patient-modal .patients-modal-continue');
    const appointmentsModal = document.getElementById('signature-appointments-modal');
    const apptBackButton = document.querySelector('#signature-appointments-modal button:first-child');
    const apptContinueButton = document.querySelector('#signature-appointments-modal button:last-child');

    continueButton.addEventListener('click', () => {
      const selectedOption = document.querySelector('input[name="due_date_option"]:checked')?.value;

      patientModal.classList.add('hidden');
      document.querySelector('#signature_request_user_id_hidden').value = document.querySelector('#signature_request_user_id').value;
      document.querySelector('#signature_request_due_by_hidden').value = document.querySelector('#signature_request_due_by').value;

      if (selectedOption === 'appointment') {
        const patientId = $("#signature_request_user_id").val();
        fetch(`/admin/signature_requests/fetch_patient_appointments?patient_id=${patientId}`)
            .then(response => response.json())
            .then(data => {
              renderAppointments(data.appointments);
              activateAppointmentCardClicks();
              appointmentsModal?.classList.remove('hidden');
            })
            .catch(error => {
              console.error('Failed to load appointments:', error);
            });
      } else {
        $('.next-step-button').removeClass('hidden');
      }
    });

    apptBackButton.addEventListener('click', () => {
      appointmentsModal.classList.add('hidden');
      patientModal.classList.remove('hidden');
    })

    apptContinueButton.addEventListener('click', () => {
      appointmentsModal.classList.add('hidden');
      $('.next-step-button').removeClass('hidden');
    })
  }

  function initRadiobuttons(){
    const radioButtons = document.querySelectorAll('input[name="due_date_option"]');
    const dueDateInput = document.getElementById("signature_request_due_by");
    radioButtons.forEach(radio => {
      radio.addEventListener("change", () => {
        if (radio.value === "custom") {
          dueDateInput.disabled = false;
        } else {
          dueDateInput.value = "";
          dueDateInput.disabled = true;
        }

        // Update ring styles
        radioButtons.forEach(rb => {
          const parent = rb.closest("label");
          if (rb.checked) {
            parent.classList.add("ring-2", "ring-[#0071e3]", "bg-[#f5f5f7]");
            parent.querySelector("div.w-5").innerHTML = `<div class="w-3 h-3 rounded-full bg-[#0071e3]"></div>`;
            parent.querySelector("div.w-5").classList.replace("border-[#d1d1d6]", "border-[#0071e3]");
          } else {
            parent.classList.remove("ring-2", "ring-[#0071e3]", "bg-[#f5f5f7]");
            parent.querySelector("div.w-5").innerHTML = ``;
            parent.querySelector("div.w-5").classList.replace("border-[#0071e3]", "border-[#d1d1d6]");
          }
        });
      });
    });

    // Trigger initial change
    document.querySelector('input[name="due_date_option"]:checked').dispatchEvent(new Event('change'));
  }

  function initSelect2Patients() {
    $('#signature_request_user_id').select2({
      selectionCssClass: "form-control",
      dropdownParent: $('#signature-patient-modal'),
      width: "100%",
      minimumInputLength: 3,
      ajax: {
        url: '/admin/patients/select2_search',
        dataType: 'json',
        delay: 250,
        data: params => ({q: params.term}),
        processResults: data => data
      }
    });

    if (isEditMode) {
      let patientData = $('#signature_request_user_id_hidden').data();
      let option = new Option(patientData.text, patientData.id, true, true);
      $('#signature_request_user_id').append(option).trigger('change');
    }

    $('.select2-selection__arrow')[0].style.display = 'none';
    $('.select2-selection.select2-selection--single.form-control')[0].style.padding = '5px';
    $('.select2-selection.select2-selection--single.form-control')[0].style.backgroundColor = '#f5f5f7';

    $("#signature_request_user_id").on("select2:select", function (e) {
      const id = e.params.data.id
      $.get(`/admin/patients/${id}.json`).done(function (response) {
        const p = response.patient;

        const fields = {
          to_name: [p.title, p.first_name, p.last_name].filter(Boolean).join(' '),
          to_address_line_1: p.address_line_1,
          to_address_line_2: p.address_line_2,
          to_address_line_3: p.address_line_3,
          to_town: p.city,
          to_county: p.county,
          to_region: p.region,
          to_postcode: p.postcode
        }
        Object.entries(fields).forEach(([k, v]) => {
          if (v) $('[data-letter-field="' + k + '"]').val(v)
        })

        const patientInfo = {
            first_name: p.first_name,
            last_name: p.last_name,
            email: p.email,
            mobile_phone: p.mobile_phone,
            dob: p.date_of_birth
        }

        fillPatientInfo(patientInfo);
      })
    })
  }

  function filterTreatmentPlans(id) {
    const treatmentPlanTemplates = document.querySelectorAll('div.doc-template[data-type="Treatment plan"]');

    treatmentPlanTemplates.forEach(template => {
      if (template.dataset.patientId !== id) {
        template.classList.add('hidden');
      } else {
        template.classList.remove('hidden');
      }
    });
  }

  function fillPatientInfo(patientInfo) {
    const patientNameBlocks = document.querySelectorAll('.document-patient-name');
    const patientDobBlocks = document.querySelectorAll('.document-patient-dob');
    const patientPhoneBlocks = document.querySelectorAll('.document-patient-phone');
    const patientEmailBlocks = document.querySelectorAll('.document-patient-email');
    const patientInitialsBlocks = document.querySelectorAll('.document-patient-initials');

    patientNameBlocks.forEach(el => {
      el.textContent = `${patientInfo.first_name} ${patientInfo.last_name}`;
    });

    patientDobBlocks.forEach(el => {
      el.textContent = formatDOB(patientInfo.dob);
    });

    patientPhoneBlocks.forEach(el => {
      el.textContent = patientInfo.mobile_phone;
    });

    patientEmailBlocks.forEach(el => {
      el.textContent = patientInfo.email;
    });

    patientInitialsBlocks.forEach(el => {
      el.textContent = getInitials(patientInfo.first_name, patientInfo.last_name);
    });
  }

  function formatDOB(dob) {
    if (!dob) return '';
    const [year, month, day] = dob.split('-');
    return `DOB: ${month}/${day}/${year}`;
  }

  function getInitials(firstName, lastName) {
    return `${firstName?.[0] ?? ''}${lastName?.[0] ?? ''}`.toUpperCase();
  }

  function renderAppointments(appointments) {
    const container = document.getElementById('signature-appointments-grid');
    container.innerHTML = '';
    const colors = ['blue', 'green', 'purple', 'amber', 'indigo'];

    appointments.forEach((appt, index) => {
      const randomColor = colors[Math.floor(Math.random() * colors.length)];
      const html = `
      <div data-appt-id='${appt.appt_id}' class="request-appointment-card rounded-xl border overflow-hidden transition-all duration-200 hover:shadow-md border-gray-200">
        <div class="bg-[#e6f0ff] px-4 py-3 flex justify-between items-center">
          <h3 class="text-[#3b82f6] font-medium whitespace-nowrap">Appointment #${appt.appt_id}</h3>
          <div class="bg-white rounded-full p-2 flex items-center gap-2 shadow-sm flex-shrink-0">
            <div class="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0 flex items-center justify-center text-white bg-gray-800">
              ${appt.dentist_first_name[0]}${appt.dentist_last_name[0]}
            </div>
            <div>
              <p class="font-medium text-sm text-[#1d1d1f]">${appt.dentist_first_name} ${appt.dentist_last_name}</p>
              <p class="text-xs text-gray-500">${appt.role}</p>
            </div>
          </div>
        </div>
        <div class="bg-white p-4 text-sm">
          <div class="flex items-center gap-2 mb-3">
            <div class="text-green-500">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-.997-6l7.07-7.071-1.414-1.414-5.656 5.657-2.829-2.829-1.414 1.414L11.003 16z" fill="currentColor"></path>
              </svg>
            </div>
            <div class="font-medium">Booked</div>
            <div class="bg-[#e6f9ed] text-[#22c55e] text-xs font-medium px-3 py-1 rounded-full">Confirmed</div>
          </div>
          <div class="flex mb-4">
            <div class="flex items-center gap-2 mr-6">
              <svg class="text-[#3b82f6]" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17 3h4a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h4V1h2v2h6V1h2v2zm-2 2H9v2H7V5H4v4h16V5h-3v2h-2V5zm5 6H4v8h16v-8z" fill="currentColor"></path>
              </svg>
              <span>${appt.calendar_booking_start}</span>
            </div>
            <div class="flex items-center gap-2">
              <svg class="text-[#3b82f6]" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm1-8h4v2h-6V7h2v5z" fill="currentColor"></path>
              </svg>
              <span>${appt.calendar_booking_duration} min</span>
            </div>
          </div>
          <div class="flex flex-wrap gap-2">
            ${appt.treatments.map(treatment =>
                `<div class="px-3 py-2 rounded-full text-xs bg-${randomColor}-50 text-${randomColor}-500">${treatment}</div>`
            ).join('')}
          </div>
        </div>
      </div>
    `;

      container.insertAdjacentHTML('beforeend', html);
    });
  }

  function activateAppointmentCardClicks() {
    const cards = document.querySelectorAll('.request-appointment-card');
    const hiddenInput = document.getElementById('signature_request_charting_appointment_id');
    const continueButton = document.querySelector('#signature-appointments-modal button:last-child');

    cards.forEach(card => {
      card.addEventListener('click', () => {
        cards.forEach(c => {
          c.classList.remove('ring-4', 'ring-[#0071e3]', 'transform', 'scale-[1.02]');
          c.classList.add('hover:shadow-md', 'border-gray-200');
        });

        card.classList.add('ring-4', 'ring-[#0071e3]', 'transform', 'scale-[1.02]');
        card.classList.remove('hover:shadow-md', 'border-gray-200');

        const apptId = card.getAttribute('data-appt-id');
        if (hiddenInput) hiddenInput.value = apptId;

        if (continueButton) {
          continueButton.disabled = false;
          continueButton.classList.remove('opacity-50', 'cursor-not-allowed');
          continueButton.classList.add('cursor-pointer');
        }
      });
    });

    if (isEditMode) {
      cards.forEach(card => {
        const apptId = card.getAttribute('data-appt-id');

        if (apptId === hiddenInput.value) {
          card.click();
        }
      })
    }
  }
});
