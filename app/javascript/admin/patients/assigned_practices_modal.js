// Assigned Practices Modal Functionality
document.addEventListener('DOMContentLoaded', function() {
  // Cache DOM elements
  const modal = document.getElementById('assigned-practices-modal');
  const openModalBtn = document.getElementById('open-assigned-practices-modal');
  const closeModalBtn = document.getElementById('close-assigned-practices-modal');
  const cancelBtn = document.getElementById('cancel-assigned-practices');
  const saveBtn = document.getElementById('save-assigned-practices');
  const searchInput = document.getElementById('practices-search-input');
  const selectedSection = document.getElementById('selected-practices-section');
  const selectedContainer = document.getElementById('selected-practices-container');
  const practicesList = document.getElementById('practices-list');

  // Exit early if modal or open button doesn't exist
  if (!modal || !openModalBtn) return;

  // Initialize selected practices on modal open
  function initializeSelectedPractices() {
    // Hide pre-selected practices from available list
    const checkedCheckboxes = modal.querySelectorAll('.practice-checkbox:checked');
    checkedCheckboxes.forEach(checkbox => {
      const practiceItem = checkbox.closest('[data-practice-id]');
      if (practiceItem) {
        practiceItem.style.display = 'none';
        practiceItem.dataset.selected = 'true';
      }
    });
  }

  // Handle checkbox changes
  function handleCheckboxChange(event) {
    const checkbox = event.target;
    const practiceItem = checkbox.closest('[data-practice-id]');
    practiceItem.dataset.selected = checkbox.checked ? 'true' : 'false';

    if (checkbox.checked) {
      moveToSelected(practiceItem);
    } else {
      moveToAvailable(practiceItem);
    }
  }

  // Move practice to selected section
  function moveToSelected(practiceItem) {
    const practiceId = practiceItem.dataset.practiceId;
    const practiceName = practiceItem.dataset.practiceName;
    const avatarElement = practiceItem.querySelector('.rounded-full');

    // Check if already in selected section (both data-practice-id and data-remove-practice)
    const existingBadge = selectedContainer.querySelector(`[data-practice-id="${practiceId}"], [data-remove-practice="${practiceId}"]`);
    if (existingBadge) return;

    // Create selected practice badge
    const selectedBadge = document.createElement('div');
    selectedBadge.className = 'flex items-center gap-2 p-1.5 pr-2 bg-gray-100 rounded-md text-sm transition-all duration-200 ease-in-out';
    selectedBadge.setAttribute('data-practice-id', practiceId);
    selectedBadge.setAttribute('data-dynamic', 'true');

    // Clone the avatar and resize it
    const avatarClone = avatarElement.cloneNode(true);
    avatarClone.className = avatarClone.className.replace('h-8 w-8', 'h-6 w-6');

    // Create the badge content
    selectedBadge.appendChild(avatarClone);

    const nameSpan = document.createElement('span');
    nameSpan.className = 'font-medium text-gray-900 whitespace-nowrap';
    nameSpan.textContent = practiceName;
    selectedBadge.appendChild(nameSpan);

    const removeButton = document.createElement('button');
    removeButton.className = 'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors hover:bg-red-100 h-5 w-5 text-gray-500 hover:text-red-600';
    removeButton.setAttribute('data-remove-practice', practiceId);
    removeButton.setAttribute('aria-label', `Remove ${practiceName}`);
    removeButton.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3.5 w-3.5">
        <path d="M18 6 6 18"></path>
        <path d="m6 6 12 12"></path>
      </svg>
    `;
    selectedBadge.appendChild(removeButton);

    // Add to selected container with animation
    selectedBadge.style.opacity = '0';
    selectedBadge.style.transform = 'scale(0.8)';
    selectedContainer.appendChild(selectedBadge);

    // Animate in
    requestAnimationFrame(() => {
      selectedBadge.style.opacity = '1';
      selectedBadge.style.transform = 'scale(1)';
    });

    // Hide from available list with animation
    practiceItem.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';
    practiceItem.style.opacity = '0';
    practiceItem.style.transform = 'translateX(-10px)';

    setTimeout(() => {
      practiceItem.style.display = 'none';
    }, 200);

    // Show selected section if hidden
    if (selectedSection.classList.contains('hidden')) {
      selectedSection.classList.remove('hidden');
      selectedSection.style.display = 'block';
    }
  }

  // Move practice back to available section
  function moveToAvailable(practiceItem) {
    const practiceId = practiceItem.dataset.practiceId;

    // Remove from selected section (both server-rendered and dynamic badges)
    const selectedBadges = selectedContainer.querySelectorAll(`[data-practice-id="${practiceId}"], [data-remove-practice="${practiceId}"]`);
    selectedBadges.forEach(badge => {
      const badgeContainer = badge.closest('.flex.items-center.gap-2');
      if (badgeContainer) {
        // Animate out
        badgeContainer.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';
        badgeContainer.style.opacity = '0';
        badgeContainer.style.transform = 'scale(0.8)';

        setTimeout(() => {
          badgeContainer.remove();

          // Hide selected section if empty
          const remainingBadges = selectedContainer.querySelectorAll('[data-practice-id], [data-remove-practice]');
          if (remainingBadges.length === 0) {
            selectedSection.classList.add('hidden');
            selectedSection.style.display = 'none';
          }
        }, 200);
      }
    });

    // Show in available list with animation
    practiceItem.style.display = 'flex';
    practiceItem.style.opacity = '0';
    practiceItem.style.transform = 'translateX(-10px)';

    requestAnimationFrame(() => {
      practiceItem.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';
      practiceItem.style.opacity = '1';
      practiceItem.style.transform = 'translateX(0)';
    });
  }

  // Handle remove practice from selected
  function handleRemovePractice(event) {
    if (event.target.closest('[data-remove-practice]')) {
      event.preventDefault();
      event.stopPropagation();

      const removeBtn = event.target.closest('[data-remove-practice]');
      const practiceId = removeBtn.dataset.removePractice;
      const checkbox = modal.querySelector(`#practice-checkbox-${practiceId}`);
      const practiceItem = modal.querySelector(`[data-practice-id="${practiceId}"]`);

      if (checkbox && practiceItem) {
        checkbox.checked = false;
        practiceItem.dataset.selected = 'false';
        moveToAvailable(practiceItem);
      }
    }
  }

  // Handle clicking on available practice (not checkbox)
  function handleAvailablePracticeClick(event) {
    // Don't trigger if clicking on checkbox or its label
    if (event.target.type === 'checkbox' || event.target.closest('input[type="checkbox"]')) {
      return;
    }

    const practiceItem = event.target.closest('[data-practice-id]');
    if (!practiceItem) return;

    const checkbox = practiceItem.querySelector('.practice-checkbox');
    if (checkbox && !checkbox.checked) {
      checkbox.checked = true;
      practiceItem.dataset.selected = 'true';
      moveToSelected(practiceItem);
    }
  }

  // Search functionality with improved filtering
  function handleSearch(event) {
    const searchTerm = event.target.value.toLowerCase();
    const practiceItems = practicesList.querySelectorAll('[data-practice-id]');
    let visibleCount = 0;

    practiceItems.forEach(item => {
      const searchText = item.dataset.practiceSearch;
      const isVisible = searchText.includes(searchTerm);

      if (isVisible) {
        item.style.display = 'flex';
        visibleCount++;
      } else {
        item.style.display = 'none';
      }
    });

    // Show/hide "no results" message if needed
    const noResultsMsg = practicesList.querySelector('.no-results-message');
    if (visibleCount === 0 && searchTerm.length > 0) {
      if (!noResultsMsg) {
        const noResults = document.createElement('div');
        noResults.className = 'no-results-message px-2 py-4 text-center text-gray-500 text-sm';
        noResults.textContent = 'No practices found matching your search.';
        practicesList.appendChild(noResults);
      }
    } else if (noResultsMsg) {
      noResultsMsg.remove();
    }
  }

  // Verify CSRF token exists
  function verifyCsrfToken() {
    const csrfToken = $('meta[name="csrf-token"]').attr('content');
    if (!csrfToken) {
      console.error('CSRF token not found. Please refresh the page.');
      toastr.error('Security token missing. Please refresh the page and try again.');
      return false;
    }
    return csrfToken;
  }

  // Event listeners

  // Add event listeners for checkboxes
  modal.addEventListener('change', function(event) {
    if (event.target.classList.contains('practice-checkbox')) {
      handleCheckboxChange(event);
    }
  });

  // Add event listener for remove buttons in selected section
  selectedContainer.addEventListener('click', handleRemovePractice);

  // Add event listener for clicking on available practices
  if (practicesList) {
    practicesList.addEventListener('click', handleAvailablePracticeClick);
  }

  // Add search functionality
  if (searchInput) {
    searchInput.addEventListener('input', handleSearch);
  }

  // Modal open/close functionality with optimized transitions

  // Open modal with performance optimizations
  openModalBtn.addEventListener('click', function() {
    // Use requestAnimationFrame for smoother animation
    requestAnimationFrame(() => {
      modal.classList.remove('hidden');
      document.body.classList.add('overflow-hidden');

      // Small delay to ensure DOM is ready
      setTimeout(() => {
        // Initialize the selected practices display
        initializeSelectedPractices();
      }, 50);

      // Focus search input
      if (searchInput) {
        searchInput.focus();
      }
    });
  });

  // Optimized close modal function
  const closeModal = function() {
    // Use requestAnimationFrame for smoother animation
    requestAnimationFrame(() => {
      modal.classList.add('hidden');
      document.body.classList.remove('overflow-hidden');
      // Clear search
      if (searchInput) {
        searchInput.value = '';
        handleSearch({ target: { value: '' } });
      }
    });
  };

  // Close with X button
  if (closeModalBtn) {
    closeModalBtn.addEventListener('click', closeModal);
  }

  // Close with Cancel button
  if (cancelBtn) {
    cancelBtn.addEventListener('click', closeModal);
  }

  // Close when clicking outside modal - with debounce to prevent multiple triggers
  let isClosing = false;
  modal.addEventListener('click', function(event) {
    if (event.target === modal && !isClosing) {
      isClosing = true;
      closeModal();
      // Reset flag after animation completes
      setTimeout(() => { isClosing = false; }, 300);
    }
  });

  // Save changes with optimized AJAX handling
  if (saveBtn) {
    saveBtn.addEventListener('click', function() {
      const patientId = this.dataset.patientId;

      // Verify CSRF token before proceeding
      const csrfToken = verifyCsrfToken();
      if (!csrfToken) return;

      // Get selected practice IDs from checked checkboxes
      const selectedCheckboxes = modal.querySelectorAll('.practice-checkbox:checked');
      const selectedPractices = Array.from(selectedCheckboxes).map(checkbox => checkbox.value);

      // Prevent multiple submissions
      if (saveBtn.disabled) return;

      // Show loading state
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<span class="inline-block animate-spin mr-2">↻</span> Saving...';

      // Make AJAX request to update assigned practices
      $.ajax({
        url: `/admin/patients/${patientId}/update_assigned_practices`,
        method: 'PATCH',
        data: {
          patient: {
            practice_ids: selectedPractices
          },
          authenticity_token: csrfToken
        },
        success: function(response) {
          // Show success message
          toastr.success('Practices updated successfully');

          // Update the UI to reflect changes - with performance optimization
          if (response.html) {
            // Use requestAnimationFrame for smoother DOM updates
            requestAnimationFrame(() => {
              $('.assigned-practices-members').html(response.html);
            });
          }

          // Close modal after a short delay to ensure UI updates complete
          setTimeout(closeModal, 100);
        },
        error: function(xhr) {
          // Show error message
          toastr.error(xhr.responseJSON?.error || 'Failed to update practices');
        },
        complete: function() {
          // Reset button state
          setTimeout(() => {
            saveBtn.disabled = false;
            saveBtn.innerHTML = 'Confirm Selection';
          }, 300);
        }
      });
    });
  }
});
