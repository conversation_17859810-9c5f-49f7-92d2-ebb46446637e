// Assigned Team Modal Functionality
document.addEventListener('DOMContentLoaded', function() {
  // Cache DOM elements
  const modal = document.getElementById('assigned-team-modal');
  const openModalBtn = document.getElementById('open-assigned-team-modal');
  const closeModalBtn = document.getElementById('close-assigned-team-modal');
  const cancelBtn = document.getElementById('cancel-assigned-team');
  const saveBtn = document.getElementById('save-assigned-team');
  const searchInput = document.getElementById('team-search-input');
  const selectedSection = document.getElementById('selected-team-section');
  const selectedContainer = document.getElementById('selected-team-container');
  const teamMembersList = document.getElementById('team-members-list');

  // Exit early if modal or open button doesn't exist
  if (!modal || !openModalBtn) return;

  // Initialize selected team members on modal open
  function initializeSelectedTeam() {
    // Hide pre-selected team members from available list
    const checkedCheckboxes = modal.querySelectorAll('.team-member-checkbox:checked');
    checkedCheckboxes.forEach(checkbox => {
      const userItem = checkbox.closest('[data-user-id]');
      if (userItem) {
        userItem.style.display = 'none';
        userItem.dataset.selected = 'true';
      }
    });
  }

  // Handle checkbox changes
  function handleCheckboxChange(event) {
    const checkbox = event.target;
    const userItem = checkbox.closest('[data-user-id]');
    userItem.dataset.selected = checkbox.checked ? 'true' : 'false';

    if (checkbox.checked) {
      moveToSelected(userItem);
    } else {
      moveToAvailable(userItem);
    }
  }

  // Move team member to selected section
  function moveToSelected(userItem) {
    const userId = userItem.dataset.userId;
    const userName = userItem.dataset.userName;
    const avatarElement = userItem.querySelector('.rounded-full');

    // Check if already in selected section (both data-user-id and data-remove-user)
    const existingBadge = selectedContainer.querySelector(`[data-user-id="${userId}"], [data-remove-user="${userId}"]`);
    if (existingBadge) return;

    // Create selected team member badge
    const selectedBadge = document.createElement('div');
    selectedBadge.className = 'flex items-center gap-2 p-1.5 pr-2 bg-gray-100 rounded-md text-sm transition-all duration-200 ease-in-out';
    selectedBadge.setAttribute('data-user-id', userId);
    selectedBadge.setAttribute('data-dynamic', 'true');

    // Clone the avatar and resize it
    const avatarClone = avatarElement.cloneNode(true);
    // Update the avatar size by modifying the user_avatar image style
    const avatarImg = avatarClone.querySelector('.user-avatar');
    if (avatarImg) {
      avatarImg.style.width = '24px';
      avatarImg.style.height = '24px';
    }

    // Create the badge content
    selectedBadge.appendChild(avatarClone);

    const nameSpan = document.createElement('span');
    nameSpan.className = 'font-medium text-gray-900 whitespace-nowrap';
    nameSpan.textContent = userName;
    selectedBadge.appendChild(nameSpan);

    const removeButton = document.createElement('button');
    removeButton.className = 'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors hover:bg-red-100 h-5 w-5 text-gray-500 hover:text-red-600';
    removeButton.setAttribute('data-remove-user', userId);
    removeButton.setAttribute('aria-label', `Remove ${userName}`);
    removeButton.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3.5 w-3.5">
        <path d="M18 6 6 18"></path>
        <path d="m6 6 12 12"></path>
      </svg>
    `;
    selectedBadge.appendChild(removeButton);

    // Add to selected container with animation
    selectedBadge.style.opacity = '0';
    selectedBadge.style.transform = 'scale(0.8)';
    selectedContainer.appendChild(selectedBadge);

    // Animate in
    requestAnimationFrame(() => {
      selectedBadge.style.opacity = '1';
      selectedBadge.style.transform = 'scale(1)';
    });

    // Hide from available list with animation
    userItem.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';
    userItem.style.opacity = '0';
    userItem.style.transform = 'translateX(-10px)';

    setTimeout(() => {
      userItem.style.display = 'none';
    }, 200);

    // Show selected section if hidden
    if (selectedSection.classList.contains('hidden')) {
      selectedSection.classList.remove('hidden');
      selectedSection.style.display = 'block';
    }
  }

  // Move team member back to available section
  function moveToAvailable(userItem) {
    const userId = userItem.dataset.userId;

    // Remove from selected section (both server-rendered and dynamic badges)
    const selectedBadges = selectedContainer.querySelectorAll(`[data-user-id="${userId}"], [data-remove-user="${userId}"]`);
    selectedBadges.forEach(badge => {
      const badgeContainer = badge.closest('.flex.items-center.gap-2');
      if (badgeContainer) {
        // Animate out
        badgeContainer.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';
        badgeContainer.style.opacity = '0';
        badgeContainer.style.transform = 'scale(0.8)';

        setTimeout(() => {
          badgeContainer.remove();

          // Hide selected section if empty
          const remainingBadges = selectedContainer.querySelectorAll('[data-user-id], [data-remove-user]');
          if (remainingBadges.length === 0) {
            selectedSection.classList.add('hidden');
            selectedSection.style.display = 'none';
          }
        }, 200);
      }
    });

    // Show in available list with animation
    userItem.style.display = 'flex';
    userItem.style.opacity = '0';
    userItem.style.transform = 'translateX(-10px)';

    requestAnimationFrame(() => {
      userItem.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';
      userItem.style.opacity = '1';
      userItem.style.transform = 'translateX(0)';
    });
  }

  // Handle remove user from selected
  function handleRemoveUser(event) {
    if (event.target.closest('[data-remove-user]')) {
      event.preventDefault();
      event.stopPropagation();

      const removeBtn = event.target.closest('[data-remove-user]');
      const userId = removeBtn.dataset.removeUser;
      const checkbox = modal.querySelector(`#user-checkbox-${userId}`);
      const userItem = modal.querySelector(`[data-user-id="${userId}"]`);

      if (checkbox && userItem) {
        checkbox.checked = false;
        userItem.dataset.selected = 'false';
        moveToAvailable(userItem);
      }
    }
  }

  // Handle clicking on available team member (not checkbox)
  function handleAvailableTeamMemberClick(event) {
    // Don't trigger if clicking on checkbox or its label
    if (event.target.type === 'checkbox' || event.target.closest('input[type="checkbox"]')) {
      return;
    }

    const userItem = event.target.closest('[data-user-id]');
    if (!userItem) return;

    const checkbox = userItem.querySelector('.team-member-checkbox');
    if (checkbox && !checkbox.checked) {
      checkbox.checked = true;
      userItem.dataset.selected = 'true';
      moveToSelected(userItem);
    }
  }

  // Search functionality with improved filtering
  function handleSearch(event) {
    const searchTerm = event.target.value.toLowerCase();
    const userItems = teamMembersList.querySelectorAll('[data-user-id]');
    let visibleCount = 0;

    userItems.forEach(item => {
      const searchText = item.dataset.userSearch;
      const isVisible = searchText.includes(searchTerm);

      if (isVisible) {
        item.style.display = 'flex';
        visibleCount++;
      } else {
        item.style.display = 'none';
      }
    });

    // Show/hide "no results" message if needed
    const noResultsMsg = teamMembersList.querySelector('.no-results-message');
    if (visibleCount === 0 && searchTerm.length > 0) {
      if (!noResultsMsg) {
        const noResults = document.createElement('div');
        noResults.className = 'no-results-message px-2 py-4 text-center text-gray-500 text-sm';
        noResults.textContent = 'No team members found matching your search.';
        teamMembersList.appendChild(noResults);
      }
    } else if (noResultsMsg) {
      noResultsMsg.remove();
    }
  }

  // Event listeners

  // Add event listeners for checkboxes
  modal.addEventListener('change', function(event) {
    if (event.target.classList.contains('team-member-checkbox')) {
      handleCheckboxChange(event);
    }
  });

  // Add event listener for remove buttons in selected section
  selectedContainer.addEventListener('click', handleRemoveUser);

  // Add event listener for clicking on available team members
  if (teamMembersList) {
    teamMembersList.addEventListener('click', handleAvailableTeamMemberClick);
  }

  // Add search functionality
  if (searchInput) {
    searchInput.addEventListener('input', handleSearch);
  }

  // Modal open/close functionality with optimized transitions

  // Open modal with performance optimizations
  openModalBtn.addEventListener('click', function() {
    // Use requestAnimationFrame for smoother animation
    requestAnimationFrame(() => {
      modal.classList.remove('hidden');
      document.body.classList.add('overflow-hidden');

      // Small delay to ensure DOM is ready
      setTimeout(() => {
        // Initialize the selected team display
        initializeSelectedTeam();
      }, 50);

      // Focus search input
      if (searchInput) {
        searchInput.focus();
      }
    });
  });

  // Optimized close modal function
  const closeModal = function() {
    // Use requestAnimationFrame for smoother animation
    requestAnimationFrame(() => {
      modal.classList.add('hidden');
      document.body.classList.remove('overflow-hidden');
      // Clear search
      if (searchInput) {
        searchInput.value = '';
        handleSearch({ target: { value: '' } });
      }
    });
  };

  // Close with X button
  if (closeModalBtn) {
    closeModalBtn.addEventListener('click', closeModal);
  }

  // Close with Cancel button
  if (cancelBtn) {
    cancelBtn.addEventListener('click', closeModal);
  }

  // Close when clicking outside modal - with debounce to prevent multiple triggers
  let isClosing = false;
  modal.addEventListener('click', function(event) {
    if (event.target === modal && !isClosing) {
      isClosing = true;
      closeModal();
      // Reset flag after animation completes
      setTimeout(() => { isClosing = false; }, 300);
    }
  });

  // Verify CSRF token exists
  function verifyCsrfToken() {
    const csrfToken = $('meta[name="csrf-token"]').attr('content');
    if (!csrfToken) {
      console.error('CSRF token not found. Please refresh the page.');
      toastr.error('Security token missing. Please refresh the page and try again.');
      return false;
    }
    return csrfToken;
  }

  // Save changes with optimized AJAX handling
  if (saveBtn) {
    saveBtn.addEventListener('click', function() {
      const patientId = this.dataset.patientId;

      // Verify CSRF token before proceeding
      const csrfToken = verifyCsrfToken();
      if (!csrfToken) return;

      // Get selected staff IDs from checked checkboxes
      const selectedCheckboxes = modal.querySelectorAll('.team-member-checkbox:checked');
      const selectedStaff = Array.from(selectedCheckboxes).map(checkbox => checkbox.value);

      // Prevent multiple submissions
      if (saveBtn.disabled) return;

      // Show loading state
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<span class="inline-block animate-spin mr-2">↻</span> Saving...';

      // Make AJAX request to update assigned staff
      $.ajax({
        url: `/admin/patients/${patientId}/update_assigned_staff`,
        method: 'PATCH',
        data: {
          patient: {
            assigned_staff_ids: selectedStaff
          },
          authenticity_token: csrfToken
        },
        success: function(response) {
          // Show success message
          toastr.success('Team members updated successfully');
          
          // Update the UI to reflect changes - with performance optimization
          if (response.html) {
            // Use requestAnimationFrame for smoother DOM updates
            requestAnimationFrame(() => {
              $('.assigned-team-members').html(response.html);
            });
          }
          
          // Close modal after a short delay to ensure UI updates complete
          setTimeout(closeModal, 100);
        },
        error: function(xhr) {
          // Show error message
          toastr.error(xhr.responseJSON?.error || 'Failed to update team members');
        },
        complete: function() {
          // Reset button state
          setTimeout(() => {
            saveBtn.disabled = false;
            saveBtn.innerHTML = 'Confirm Selection';
          }, 300);
        }
      });
    });
  }
});
