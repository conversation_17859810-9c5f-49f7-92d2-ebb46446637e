// Payment Plans Modal Functionality
document.addEventListener('DOMContentLoaded', function() {
  // DOM elements
  const addPaymentPlanBtn = document.getElementById('add-payment-plan-btn');
  const editPaymentPlansBtn = document.getElementById('edit-payment-plans-btn');
  const paymentPlansModal = document.getElementById('payment-plans-modal');
  const closePaymentPlanModal = document.getElementById('close-payment-plan-modal');
  const cancelPaymentPlanBtn = document.getElementById('cancel-payment-plan');
  const addPaymentPlanBtn2 = document.getElementById('add-payment-plan');
  const paymentPlanSelect = document.getElementById('payment-plan-select');
  const paymentPlansContainer = document.getElementById('payment-plans-container');
  
  if (!addPaymentPlanBtn) return; // Exit if elements don't exist on this page
  
  // Open modal when add button is clicked
  addPaymentPlanBtn.addEventListener('click', function() {
    paymentPlansModal.classList.remove('hidden');
  });
  
  // Close modal when close button is clicked
  closePaymentPlanModal.addEventListener('click', function() {
    paymentPlansModal.classList.add('hidden');
  });
  
  // Close modal when cancel button is clicked
  cancelPaymentPlanBtn.addEventListener('click', function() {
    paymentPlansModal.classList.add('hidden');
  });
  
  // Toggle edit mode when edit button is clicked
  editPaymentPlansBtn.addEventListener('click', function() {
    const removeButtons = document.querySelectorAll('.remove-plan-btn');
    removeButtons.forEach(btn => {
      btn.classList.toggle('hidden');
    });
  });
  
  // Add payment plan when add button in modal is clicked
  addPaymentPlanBtn2.addEventListener('click', function() {
    const selectedPlanId = paymentPlanSelect.value;
    const selectedPlanText = paymentPlanSelect.options[paymentPlanSelect.selectedIndex]?.text || 'Unknown Plan';

    if (!selectedPlanId) {
      toastr.error('Please select a payment plan');
      return;
    }
    
    // Just call the function, let the browser handle multiple clicks
    addPaymentPlan(selectedPlanId, selectedPlanText);
  });
  
  // Setup event delegation for remove buttons
  document.addEventListener('click', function(event) {
    if (event.target.closest('.remove-plan-btn')) {
      const planItem = event.target.closest('.payment-plan-item');
      const planId = planItem.dataset.planId;
      
      removePaymentPlan(planId, planItem);
    }
  });
  
  // Function to add a payment plan
  function addPaymentPlan(planId, planName) {
    const patientId = document.querySelector('meta[name="patient-id"]')?.content;
    if (!patientId) {
      resetSubmitButton();
      return;
    }
    
    // Create data object
    const data = {
      authenticity_token: $('meta[name="csrf-token"]').attr('content'),
      patient: {
        cot_payment_plan_ids: [planId]
      }
    };
    
    // Clear any existing tippy instances to prevent duplicates
    const existingTippies = document.querySelectorAll('[data-tippy-root]');
    existingTippies.forEach(tippy => tippy.remove());

    console.log('Making AJAX request to add payment plan');
    console.log('Request URL:', `/admin/patients/${patientId}/add_payment_plan`);
    console.log('Request data:', data);

    // Make AJAX request to add payment plan
    $.ajax({
      url: `/admin/patients/${patientId}/add_payment_plan`,
      method: 'POST',
      data: data,
      complete: function(xhr, status) {
        console.log('AJAX request complete. Status:', status, 'Response:', xhr.responseText);
        resetSubmitButton();
      },
      success: function(response) {
        if (response.success) {
          // Generate random color from our color set
          const colors = ['green', 'amber', 'blue', 'purple', 'cyan'];
          const randomColor = colors[Math.floor(Math.random() * colors.length)];
          
          // Create new payment plan element
          const newPlanHtml = `
            <div class="payment-plan-item flex flex-col items-center text-center" data-plan-id="${planId}" data-plan-name="${planName}">
              <div class="absolute top-1 right-1">
                <button type="button" class="remove-plan-btn hidden bg-red-100 hover:bg-red-200 text-red-600 rounded-full p-1 transition-colors duration-200">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div class="bg-${randomColor}-100 rounded-full w-12 h-12 flex items-center justify-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-${randomColor}-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <span class="text-[12px] font-medium text-gray-700 mt-1.5 w-full text-center">${planName}</span>
            </div>
          `;
          
          // Add to container
          paymentPlansContainer.insertAdjacentHTML('beforeend', newPlanHtml);
          
          // Close modal and reset select
          paymentPlansModal.classList.add('hidden');
          paymentPlanSelect.value = '';
          
          // Update header badges directly
          const badgesContainer = document.querySelector('.payment-plan-badges');

          if (badgesContainer) {
            // Get server-rendered badges (if any)
            const serverBadges = Array.from(badgesContainer.querySelectorAll('span:not([data-plus-badge])'));
            const serverBadgeTexts = serverBadges.map(b => b.textContent.trim());

            // Get all payment plan items from the modal (newly added ones)
            const modalPlanItems = Array.from(document.querySelectorAll('.payment-plan-item'));
            const modalPlanNames = modalPlanItems.map(item => item.dataset.planName);

            // Combine server and modal plans, removing duplicates
            const allPlanNames = [...new Set([...serverBadgeTexts, ...modalPlanNames])];

            console.log('Server badges:', JSON.stringify(serverBadgeTexts));
            console.log('Modal plans:', JSON.stringify(modalPlanNames));
            console.log('All plan names:', JSON.stringify(allPlanNames));

            // If no plans, clear the container and return
            if (allPlanNames.length === 0) {
              badgesContainer.innerHTML = '';
              return;
            }

            // Clear existing badges but keep server-rendered ones in memory
            badgesContainer.innerHTML = '';
            console.log('Cleared existing badges');

            // Add the first two badges
            const badgesToShow = allPlanNames.slice(0, 2);
            console.log('Badges to show:', JSON.stringify(badgesToShow));

            badgesToShow.forEach(planName => {
              // Try to find an existing server-rendered badge first
              const existingBadge = serverBadges.find(b => b.textContent.trim() === planName);

              if (existingBadge) {
                // Use the existing server-rendered badge
                console.log('Reusing server badge:', planName);
                badgesContainer.appendChild(existingBadge);
              } else {
                // Create a new badge for plans added via modal
                const badge = document.createElement('span');
                badge.className = 'px-2 py-0.5 text-xs font-medium bg-gradient-to-r from-[#e8f5f0] to-[#daf0e7] text-[#34a67f] rounded-md shadow-sm';
                badge.textContent = planName;
                console.log('Adding new badge:', planName);
                badgesContainer.appendChild(badge);
              }
            });

            // If we have more than 2 plans total, add a +X badge
            const totalPlans = allPlanNames.length;
            console.log('Total plans:', totalPlans);

            if (totalPlans > 2) {
              const plusCount = totalPlans - 2;
              console.log('Adding +' + plusCount + ' badge for additional plans');

              // Remove any existing +X badge
              const existingPlusBadge = badgesContainer.querySelector('[data-plus-badge]');
              if (existingPlusBadge) {
                existingPlusBadge.remove();
              }

              const newPlusBadge = document.createElement('span');
              newPlusBadge.className = 'px-2 py-0.5 text-xs font-medium bg-gradient-to-r from-[#e8f5f0] to-[#daf0e7] text-[#34a67f] rounded-md shadow-sm';
              newPlusBadge.textContent = '+' + plusCount;
              newPlusBadge.setAttribute('data-plus-badge', 'true');

              // Get all plan names for the tooltip (all plans except the first two)
              const additionalPlans = allPlanNames.slice(2);
              const tooltipContent = additionalPlans.join('<br>');
              console.log('Additional plans for tooltip:', JSON.stringify(additionalPlans));
              console.log('Tooltip content:', tooltipContent);

              // Store tooltip content in data attribute for Tippy
              newPlusBadge.setAttribute('data-tippy-content', tooltipContent);

              // Add to container
              badgesContainer.appendChild(newPlusBadge);
              console.log('Added +X badge to DOM');

              // Initialize tippy for the new badge
              if (window.tippy) {
                console.log('Initializing tippy...');
                // First destroy any existing tippy instances on this element
                const existingTippy = tippy(newPlusBadge);
                if (existingTippy) {
                  existingTippy.destroy();
                }

                // Create new tippy instance
                tippy(newPlusBadge, {
                  content: tooltipContent,
                  allowHTML: true,
                  placement: 'bottom',
                  theme: 'light',
                  arrow: true,
                  onShow(instance) {
                    // Ensure we have the latest content when shown
                    const currentContent = instance.reference.getAttribute('data-tippy-content');
                    if (currentContent !== instance.props.content) {
                      instance.setContent(currentContent);
                    }
                  }
                });
                console.log('Tippy instance created');
              }
            }
          }

          toastr.success(`Added ${planName} payment plan`);
        } else {
          toastr.error(response.message || 'Failed to add payment plan');
        }
      },
      error: function(xhr, status, error) {
        console.error('Error adding payment plan:', error);
        toastr.error('Failed to add payment plan');
      }
    });
  }
  
  // Function to reset the submit button state
  function resetSubmitButton() {
    console.log('Resetting submit button');
    const submitButton = document.getElementById('submit-payment-plan');
    if (submitButton) {
      submitButton.disabled = false;
      submitButton.innerHTML = '<i class="fas fa-plus mr-1"></i> Add Payment Plan';
      console.log('Button reset complete - disabled:', submitButton.disabled);
    } else {
      console.error('Submit button not found!');
    }
  }

  // Function to update payment plan badges in header when removing a plan
  function updateBadgesAfterRemoval() {
    const badgesContainer = document.querySelector('.payment-plan-badges');
    if (!badgesContainer) return;

    const badges = Array.from(badgesContainer.querySelectorAll('span:not([data-plus-badge])'));
    const plusBadge = badgesContainer.querySelector('span[data-plus-badge]');

    if (badges.length > 2) {
      // If we have more than 2 badges, update the +X badge
      if (plusBadge) {
        const plusCount = badges.length - 2; // Show +1 for 3rd plan, +2 for 4th, etc.
        plusBadge.textContent = `+${plusCount}`;

        // Update tooltip with all plan names (all except the first two)
        const allPlanNames = badges.slice(2).map(b => b.textContent);
        plusBadge.setAttribute('data-tippy-content', allPlanNames.join('<br>'));

        // Reinitialize tippy
        if (window.tippy) {
          tippy(plusBadge, {
            content: plusBadge.getAttribute('data-tippy-content'),
            allowHTML: true,
            placement: 'bottom',
            theme: 'light',
            arrow: true
          });
        }
      } else {
        // If we have more than 2 badges but no +X badge, add one
        const plusCount = badges.length - 2;
        const newPlusBadge = document.createElement('span');
        newPlusBadge.className = 'px-1.5 py-0.5 text-xs font-medium bg-gradient-to-r from-[#e8f5f0] to-[#daf0e7] text-[#34a67f] rounded-md shadow-sm';
        newPlusBadge.textContent = `+${plusCount}`;
        newPlusBadge.setAttribute('data-plus-badge', 'true');

        // Set tooltip with all plan names (all except the first two)
        const allPlanNames = badges.slice(2).map(b => b.textContent);
        newPlusBadge.setAttribute('data-tippy-content', allPlanNames.join('<br>'));

        badgesContainer.appendChild(newPlusBadge);

        // Initialize tippy for the tooltip
        if (window.tippy) {
          tippy(newPlusBadge, {
            content: newPlusBadge.getAttribute('data-tippy-content'),
            allowHTML: true,
            placement: 'bottom',
            theme: 'light',
            arrow: true
          });
        }
      }
    } else if (plusBadge) {
      // If we have 2 or fewer badges but still have a +X badge, remove it
      plusBadge.remove();
    }
  }

  // Function to remove a payment plan
  let isRemoving = false;
  function removePaymentPlan(planId, planElement) {
    // Prevent multiple removals
    if (isRemoving) {
      return;
    }
    
    const patientId = document.querySelector('meta[name="patient-id"]')?.content;
    if (!patientId) return;
    
    isRemoving = true;
    
    // Get the plan name before removing it
    const planName = planElement.querySelector('span.text-[12px]')?.textContent;

    // Show loading state
    const removeButton = planElement.querySelector('.remove-plan-btn');
    if (removeButton) {
      removeButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
      removeButton.style.pointerEvents = 'none';
    }

    // Make AJAX request to remove payment plan
    $.ajax({
      url: `/admin/patients/${patientId}/remove_payment_plan`,
      method: 'DELETE',
      data: {
        authenticity_token: $('meta[name="csrf-token"]').attr('content'),
        plan_id: planId
      },
      success: function(response) {
        if (response.success) {
          // Remove the plan element
          planElement.remove();

          // Update header badges
          if (planName) {
            const badgesContainer = document.querySelector('.payment-plan-badges');
            if (badgesContainer) {
              // Find and remove the badge with matching text
              const badgeToRemove = Array.from(badgesContainer.querySelectorAll('span:not([data-plus-badge])'))
                .find(badge => badge.textContent === planName);

              if (badgeToRemove) {
                badgeToRemove.remove();

                // Update the +X badge if it exists
                updateBadgesAfterRemoval();
              }
            }
          }

          toastr.success('Payment plan removed');
        } else {
          toastr.error(response.message || 'Failed to remove payment plan');
          // Reset remove button
          if (removeButton) {
            removeButton.innerHTML = '<i class="fa fa-times"></i>';
            removeButton.style.pointerEvents = 'auto';
          }
        }
        isRemoving = false;
      },
      error: function() {
        toastr.error('Failed to remove payment plan');
        // Reset remove button
        if (removeButton) {
          removeButton.innerHTML = '<i class="fa fa-times"></i>';
          removeButton.style.pointerEvents = 'auto';
        }
        isRemoving = false;
      }
    });
  }
});
