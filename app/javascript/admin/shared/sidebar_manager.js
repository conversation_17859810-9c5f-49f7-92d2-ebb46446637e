/**
 * Global Sidebar Manager
 * 
 * This module manages all sidebars in the application to prevent overlap.
 * It ensures that only one sidebar is open at a time.
 */

// Global sidebar manager singleton
const SidebarManager = {
  // Track all registered sidebars
  sidebars: {},

  // Track if overlay click handler is initialized
  overlayClickHandlerInitialized: false,
  
  // Register a sidebar with the manager
  register: function(id, openFn, closeFn) {
    this.sidebars[id] = {
      id: id,
      isOpen: false,
      open: openFn,
      close: closeFn
    };
    
    console.log(`Sidebar registered: ${id}`);
    return this.sidebars[id];
  },
  
  // Open a specific sidebar and close all others
  open: function(id) {
    console.log(`Opening sidebar: ${id}`);

    const wasAnySidebarOpen = this.isAnySidebarOpen();

    // Close all other open sidebars
    Object.values(this.sidebars).forEach(sidebar => {
      if (sidebar.id !== id && sidebar.isOpen) {
        console.log(`Closing other sidebar: ${sidebar.id}`);
        sidebar.close();
        sidebar.isOpen = false;
      }
    });

    // Open the requested sidebar
    const sidebarToOpen = this.sidebars[id];
    if (sidebarToOpen) {
      sidebarToOpen.open();
      sidebarToOpen.isOpen = true;

      // If no sidebar was open before, ensure overlay is shown
      if (!wasAnySidebarOpen) {
        this.showOverlay();
      }
    } else {
      console.error(`Attempted to open unregistered sidebar: ${id}`);
    }
  },
  
  // Close a specific sidebar
  close: function(id) {
    console.log(`Closing sidebar: ${id}`);
    const sidebarToClose = this.sidebars[id];
    if (sidebarToClose) {
      sidebarToClose.close();
      sidebarToClose.isOpen = false;

      // If no sidebars are open after closing, hide the overlay
      if (!this.isAnySidebarOpen()) {
        this.hideOverlay();
      }
    } else {
      console.error(`Attempted to close unregistered sidebar: ${id}`);
    }
  },

  // Show the shared overlay
  showOverlay: function() {
    const overlay = document.getElementById('shared-sidebar-overlay');
    if (overlay) {
      overlay.classList.remove('hidden');
      overlay.style.opacity = '1';

      // Initialize overlay click handler if not already done
      this.initializeOverlayClickHandler();
    }
  },

  // Hide the shared overlay
  hideOverlay: function() {
    const overlay = document.getElementById('shared-sidebar-overlay');
    if (overlay) {
      overlay.style.opacity = '0';
      setTimeout(() => {
        overlay.classList.add('hidden');
      }, 300);
    }
  },
  
  // Check if any sidebar is open
  isAnySidebarOpen: function() {
    return Object.values(this.sidebars).some(sidebar => sidebar.isOpen);
  },
  
  // Get all open sidebars
  getOpenSidebars: function() {
    return Object.values(this.sidebars).filter(sidebar => sidebar.isOpen);
  },

  // Initialize overlay click handler
  initializeOverlayClickHandler: function() {
    if (this.overlayClickHandlerInitialized) {
      return;
    }

    const overlay = document.getElementById('shared-sidebar-overlay');
    if (overlay) {
      overlay.addEventListener('click', () => {
        // Close all open sidebars when overlay is clicked
        const openSidebars = this.getOpenSidebars();
        openSidebars.forEach(sidebar => {
          this.close(sidebar.id);
        });
      });

      this.overlayClickHandlerInitialized = true;
      console.log('Overlay click handler initialized');
    }
  }
};

// Export the singleton
export default SidebarManager;
