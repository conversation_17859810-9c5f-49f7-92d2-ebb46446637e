// Simple jQuery-based notification toggle functionality
// This avoids the complex NotificationManager class and provides a standalone solution

// Import the global sidebar manager
import SidebarManager from '../shared/sidebar_manager';

// Create a simple internal implementation for the sidebar
const notificationSidebarImpl = {
  open: function() {
    const sidebar = document.getElementById('notification-sidebar');

    if (sidebar) {
      // Use Tailwind transform classes for smooth animation
      sidebar.classList.remove('translate-x-full');
    }
  },

  close: function() {
    const sidebar = document.getElementById('notification-sidebar');

    if (sidebar) {
      // Use Tailwind transform classes for smooth animation
      sidebar.classList.add('translate-x-full');
    }
  }
};

// Helper function for colorful logging
function colorLog(message, color, background, emoji) {
  console.log(
    `%c${emoji} ${message}`,
    `color: ${color}; background: ${background}; font-size: 12px; font-weight: bold; padding: 5px; border-radius: 5px;`
  );
}

// Log with different colors for different types of messages
function logInfo(message) { colorLog(message, 'white', '#3498db', '🔵'); }
function logSuccess(message) { colorLog(message, 'white', '#2ecc71', '✅'); }
function logWarning(message) { colorLog(message, 'black', '#f39c12', '⚠️'); }
function logError(message) { colorLog(message, 'white', '#e74c3c', '❌'); }

// Register the notification sidebar with the SidebarManager
function registerNotificationSidebar() {
  // Only register if not already registered
  if (!SidebarManager.sidebars['notification-sidebar']) {
    SidebarManager.register('notification-sidebar',
      () => notificationSidebarImpl.open(),
      () => notificationSidebarImpl.close()
    );
    console.log('Notification sidebar registered with SidebarManager');
  }
}

// Main function that runs when document is ready
$(document).ready(function() {
  // Register the notification sidebar with the SidebarManager
  registerNotificationSidebar();
  logInfo('Notification toggle script initialized');
  
  // Check if notification elements exist
  const $notificationToggle = $('#notification-toggle');
  const $notificationSidebar = $('#notification-sidebar');
  const $overlay = $('#shared-sidebar-overlay');
  const $closeNotifications = $('#close-notifications');

  // Log element status
  logInfo(`Notification toggle element found: ${$notificationToggle.length > 0}`);
  logInfo(`Notification sidebar element found: ${$notificationSidebar.length > 0}`);
  logInfo(`Shared overlay element found: ${$overlay.length > 0}`);
  logInfo(`Close button element found: ${$closeNotifications.length > 0}`);

  // Only initialize if required elements exist
  if ($notificationToggle.length && $notificationSidebar.length) {
    logSuccess('All required elements found, initializing event handlers');
    
    // Log current classes
    logInfo(`Notification sidebar classes: ${$notificationSidebar.attr('class')}`);
    logInfo(`Overlay classes: ${$overlay.attr('class')}`);
    
    // Toggle notification sidebar when bell icon is clicked
    // Use only the jQuery handler to avoid conflicts
    $notificationToggle.on('click', function(e) {
      logInfo('🔔 Notification toggle clicked');
      e.preventDefault();
      e.stopPropagation();

      // Make sure the sidebar is registered
      registerNotificationSidebar();

      // Check if this sidebar is currently open
      const isOpen = SidebarManager.sidebars['notification-sidebar']?.isOpen;

      if (isOpen) {
        // If it's open, close it
        SidebarManager.close('notification-sidebar');
        logInfo('Closing notification sidebar');
      } else {
        // If it's closed, open it
        SidebarManager.open('notification-sidebar');
        logInfo('Opening notification sidebar');

        // Reinitialize notification buttons if the function exists
        if (typeof initNotificationButtons === 'function') {
          logInfo('Reinitializing notification buttons');
          initNotificationButtons();
        }
      }

      logSuccess('Sidebar toggle completed');
    });
    
    // Close notification sidebar when close button is clicked
    $closeNotifications.on('click', function() {
      logInfo('Close button clicked');
      closeNotificationSidebar();
    });
    
    // Function to close the notification sidebar
    function closeNotificationSidebar() {
      logInfo('Closing notification sidebar');
      
      // Make sure the sidebar is registered
      registerNotificationSidebar();
      
      // Use the global sidebar manager to handle closing
      SidebarManager.close('notification-sidebar');
      
      logSuccess('Sidebar closed');
    }
  } else {
    logError('Some notification elements not found in the DOM');
  }
});
