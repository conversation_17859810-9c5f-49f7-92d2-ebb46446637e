# Windsurf Development Rules

## Frontend Setup & Build
- Tailwind is installed via CDN
- Run `yarn build` after any JS changes
- Run `yarn build` after any CSS changes

## Framework Restrictions
- **Never use Stimulus or Turbo**
- No Stimulus controllers
- No reactive templates
- Avoid over-architecting - keep it simple

## File Organization
- Files should not exceed 500 lines (HTML files may exceed if necessary)
- Files must never exceed 2000 lines (HTML files may exceed if splitting isn't possible)
- Split large view files into partials wherever possible
- No inline scripts - move to separate JS files
- No inline styles - move to separate CSS files

## Styling Standards
- All styling must use Tailwind classes where possible
- Remove any inline styles and put in CSS files

## JavaScript Standards
- **Use jQuery instead of vanilla JavaScript**
- Remove any inline scripts and put in JS files
- When targeting elements via JavaScript, use unique classes or `data-*` attributes
- **Never use Tailwind classes for JS targeting**
- Don't rely on generic classes or tag names (e.g., `.btn`, `div`, `span`) for JS behavior
- Keep JavaScript simple, maintainable, and easily debuggable

## Code Quality
- **No comments in code** - remove all comments
- Thin controllers
- Thin models
- **Fat service objects** - main place for business logic
- Services should be easily testable

## Architecture Philosophy
- Keep things simple, maintainable, and easily debuggable
- Services contain business logic and should be testable
- Avoid over-engineering solutions